package com.example.gymbro.features.workout.json.processor

import com.example.gymbro.features.workout.json.core.JsonConstants
import com.example.gymbro.shared.models.exercise.ExerciseDto
import com.example.gymbro.shared.models.exercise.ExerciseSetDto
import com.example.gymbro.shared.models.workout.WorkoutSessionDto
import com.example.gymbro.shared.models.workout.SessionStatus
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber

/**
 * Session JSON 处理器 v2.0 - 全功能标准化版本
 *
 * 🎯 核心重构：基于ExerciseJsonProcessor标准化模式
 * - 增加动态字段修改功能，支持实时编辑
 * - 实现事务性批量更新，确保数据一致性
 * - 强化错误处理和容错机制
 * - 提供完整的CRUD操作集
 *
 * 功能特性：
 * - WorkoutSessionDto数据的JSON序列化/反序列化
 * - 动态字段修改（Session状态、时间、备注等）
 * - 批量更新操作，支持复杂的数据修改
 * - 数据完整性验证和回滚机制
 * - 丰富的调试日志和错误处理
 *
 * <AUTHOR> AI Assistant
 * @since 2.0.0 (标准化重构版本)
 */
object SessionJsonProcessor {

    private val json = Json {
        prettyPrint = false
        ignoreUnknownKeys = true
        encodeDefaults = true
    }

    /**
     * SessionExerciseUiModel → ExerciseDto JSON
     * 基于原有的 toJson() 方法简化实现
     */
    fun sessionExerciseToJson(
        exerciseId: String,
        exerciseName: String,
        imageUrl: String? = null,
        videoUrl: String? = null,
        sets: List<SessionSetData>,
        restTimeSeconds: Int = JsonConstants.Defaults.DEFAULT_REST_TIME,
        notes: String? = null,
    ): String {
        return try {
            val exerciseDto = ExerciseDto(
                id = exerciseId,
                name = exerciseName,
                imageUrl = imageUrl,
                videoUrl = videoUrl,
                targetSets = sets.map { set ->
                    ExerciseSetDto(
                        id = set.id,
                        weight = set.weight ?: 0f,
                        reps = set.reps ?: 0,
                        isCompleted = set.isCompleted,
                        completedAt = if (set.isCompleted) set.completedAt else null,
                    )
                },
                completedSets = emptyList(), // 不重复填充，targetSets 已包含完成状态
                restTimeSeconds = restTimeSeconds,
                notes = notes ?: "",
            )

            json.encodeToString(exerciseDto)
        } catch (e: Exception) {
            Timber.e(e, "Session JSON 转换失败: $exerciseName")
            createSessionFallbackJson(exerciseId, exerciseName)
        }
    }

    /**
     * ExerciseDto → SessionExerciseUpdateData 转换
     * 用于 Session 模式下的数据更新
     */
    fun exerciseDtoToUpdateData(exerciseDto: ExerciseDto): SessionExerciseUpdateData {
        return try {
            SessionExerciseUpdateData(
                exerciseId = exerciseDto.id,
                sets = exerciseDto.targetSets.map { set ->
                    SessionSetData(
                        id = set.id,
                        weight = set.weight,
                        reps = set.reps,
                        isCompleted = set.isCompleted,
                        completedAt = set.completedAt,
                    )
                },
                restTimeSeconds = exerciseDto.restTimeSeconds,
                notes = exerciseDto.notes.takeIf { it.isNotBlank() },
            )
        } catch (e: Exception) {
            Timber.e(e, "ExerciseDto 转换为 UpdateData 失败: ${exerciseDto.name}")
            SessionExerciseUpdateData(
                exerciseId = exerciseDto.id,
                sets = emptyList(),
                restTimeSeconds = JsonConstants.Defaults.DEFAULT_REST_TIME,
                notes = null,
            )
        }
    }

    /**
     * JSON 字符串 → ExerciseDto
     */
    fun fromJson(jsonString: String): ExerciseDto? {
        return try {
            json.decodeFromString<ExerciseDto>(jsonString)
        } catch (e: Exception) {
            Timber.e(e, "Session JSON 反序列化失败")
            null
        }
    }

    /**
     * 验证 Session JSON 格式
     */
    fun validateSessionJson(jsonString: String): Boolean {
        return try {
            val exerciseDto = json.decodeFromString<ExerciseDto>(jsonString)
            // 基础验证
            exerciseDto.id.isNotBlank() && exerciseDto.name.isNotBlank()
        } catch (e: Exception) {
            Timber.e(e, "Session JSON 验证失败")
            false
        }
    }

    /**
     * 批量转换 Session 数据为 JSON
     */
    fun batchSessionToJson(sessionExercises: List<SessionExerciseData>): List<String> {
        return sessionExercises.map { exercise ->
            sessionExerciseToJson(
                exerciseId = exercise.exerciseId,
                exerciseName = exercise.exerciseName,
                imageUrl = exercise.imageUrl,
                videoUrl = exercise.videoUrl,
                sets = exercise.sets,
                restTimeSeconds = exercise.restTimeSeconds,
                notes = exercise.notes,
            )
        }
    }

    /**
     * 获取休息时间（三层优先级逻辑）
     */
    fun getRestTimeSeconds(
        sessionOverride: Int?,
        originalRestSeconds: Int?,
        defaultRestTime: Int = JsonConstants.Defaults.DEFAULT_REST_TIME,
    ): Int {
        // 1. Session 手动覆盖（最高优先级）
        if (sessionOverride != null && sessionOverride > 0) return sessionOverride

        // 2. 原始配置（中等优先级）
        if (originalRestSeconds != null && originalRestSeconds > 0) return originalRestSeconds

        // 3. 系统默认（兜底策略）
        return defaultRestTime
    }

    /**
     * 创建 Session fallback JSON
     */
    private fun createSessionFallbackJson(exerciseId: String, exerciseName: String): String {
        return try {
            val fallbackJson = """
            {
                "id": "$exerciseId",
                "name": "$exerciseName",
                "targetSets": [],
                "completedSets": [],
                "restTimeSeconds": ${JsonConstants.Defaults.DEFAULT_REST_TIME},
                "notes": ""
            }
            """.trimIndent()
            fallbackJson
        } catch (e: Exception) {
            Timber.e(e, "Session fallback JSON 创建失败")
            """{"id":"$exerciseId","name":"$exerciseName","targetSets":[],"restTimeSeconds":60,"notes":""}"""
        }
    }

    /**
     * 安全的 Session JSON 转换（带大小检查）
     */
    fun safeSessionToJson(
        exerciseId: String,
        exerciseName: String,
        sets: List<SessionSetData>,
        restTimeSeconds: Int = JsonConstants.Defaults.DEFAULT_REST_TIME,
        notes: String? = null,
    ): String? {
        val jsonString =
            sessionExerciseToJson(exerciseId, exerciseName, null, null, sets, restTimeSeconds, notes)
        return if (jsonString.length <= JsonConstants.MAX_PAYLOAD_SIZE) {
            jsonString
        } else {
            Timber.w("Session JSON 过大: ${jsonString.length} > ${JsonConstants.MAX_PAYLOAD_SIZE}")
            null
        }
    }

    /**
     * 合并 Session 数据更新
     */
    fun mergeSessionUpdate(
        base: SessionExerciseUpdateData,
        update: SessionExerciseUpdateData,
    ): SessionExerciseUpdateData {
        return SessionExerciseUpdateData(
            exerciseId = update.exerciseId.takeIf { it.isNotBlank() } ?: base.exerciseId,
            sets = if (update.sets.isNotEmpty()) update.sets else base.sets,
            restTimeSeconds = if (update.restTimeSeconds > 0) update.restTimeSeconds else base.restTimeSeconds,
            notes = update.notes?.takeIf { it.isNotBlank() } ?: base.notes,
        )
    }

    // ==================== 动态字段修改方法（新增标准化功能）====================

    /**
     * 更新Session的状态
     * 🔥 标准化功能：动态修改Session状态，支持实时状态切换
     */
    fun updateSessionStatus(jsonString: String, newStatus: com.example.gymbro.shared.models.workout.SessionStatus): String {
        return try {
            val sessionDto = json.decodeFromString<com.example.gymbro.shared.models.workout.WorkoutSessionDto>(jsonString)
            
            Timber.d("🔧 [SESSION-STATUS-UPDATE] 更新Session状态: ${sessionDto.id} ${sessionDto.status} -> $newStatus")
            
            val updatedSession = sessionDto.copy(
                status = newStatus,
                // 状态相关的时间戳更新
                endAt = if (newStatus == com.example.gymbro.shared.models.workout.SessionStatus.COMPLETED) {
                    System.currentTimeMillis()
                } else sessionDto.endAt
            )
            
            val result = json.encodeToString(updatedSession)
            
            // 🔥 验证更新结果
            val verifySession = json.decodeFromString<com.example.gymbro.shared.models.workout.WorkoutSessionDto>(result)
            Timber.d("🔧 [SESSION-STATUS-UPDATE] 验证结果: Session ${verifySession.id} 状态为 ${verifySession.status}")
            
            result
        } catch (e: Exception) {
            Timber.e(e, "更新Session状态失败: $newStatus")
            jsonString
        }
    }

    /**
     * 更新Session的倒计时时间
     * 🔥 标准化功能：动态修改倒计时，支持休息时间管理
     */
    fun updateSessionCountdown(jsonString: String, countdownEndTime: Long?): String {
        return try {
            val sessionDto = json.decodeFromString<com.example.gymbro.shared.models.workout.WorkoutSessionDto>(jsonString)
            
            Timber.d("🔧 [SESSION-COUNTDOWN-UPDATE] 更新倒计时: ${sessionDto.id} -> $countdownEndTime")
            
            val updatedSession = sessionDto.copy(
                currentCountdown = countdownEndTime
            )
            
            json.encodeToString(updatedSession)
        } catch (e: Exception) {
            Timber.e(e, "更新Session倒计时失败: $countdownEndTime")
            jsonString
        }
    }

    /**
     * 更新Session中特定动作的组数据
     * 🔥 标准化功能：动态修改动作组数据，支持训练过程中的实时编辑
     */
    fun updateSessionExerciseSet(
        jsonString: String,
        exerciseId: String,
        setOrderNo: Int,
        weight: Float? = null,
        reps: Int? = null,
        completedAt: Long? = null,
        restTimeSeconds: Int? = null
    ): String {
        return try {
            val sessionDto = json.decodeFromString<com.example.gymbro.shared.models.workout.WorkoutSessionDto>(jsonString)
            
            Timber.d("🔧 [SESSION-SET-UPDATE] 更新动作组: exerciseId=$exerciseId, setNo=$setOrderNo")
            
            val updatedExercises = sessionDto.exercises.map { exercise ->
                if (exercise.exerciseId == exerciseId) {
                    val updatedSets = exercise.sets.map { set ->
                        if (set.orderNo == setOrderNo) {
                            Timber.d("🔧 [SESSION-SET-UPDATE] 找到匹配组: 组$setOrderNo")
                            set.copy(
                                weight = weight ?: set.weight,
                                reps = reps ?: set.reps,
                                completedAt = completedAt ?: set.completedAt,
                                restTimeSeconds = restTimeSeconds ?: set.restTimeSeconds
                            )
                        } else {
                            set
                        }
                    }
                    exercise.copy(sets = updatedSets)
                } else {
                    exercise
                }
            }
            
            val updatedSession = sessionDto.copy(
                exercises = updatedExercises
            )
            
            json.encodeToString(updatedSession)
        } catch (e: Exception) {
            Timber.e(e, "更新Session动作组失败: exerciseId=$exerciseId, setNo=$setOrderNo")
            jsonString
        }
    }

    // ==================== 批量更新方法（新增标准化功能）====================

    /**
     * 批量更新Session数据 - 事务性处理版本
     * 🔥 核心标准化功能：基于ExerciseJsonProcessor模式的批量更新
     */
    fun batchUpdateSession(jsonString: String, updates: List<SessionUpdateData>): String {
        if (updates.isEmpty()) {
            Timber.d("🔥 [SESSION-BATCH-UPDATE] 批量更新列表为空，返回原始数据")
            return jsonString
        }

        return try {
            // 🔥 事务性处理：先验证所有更新操作的有效性
            val originalJson = jsonString
            var currentJson = originalJson
            val processedUpdates = mutableListOf<String>()

            Timber.d("🔥 [SESSION-BATCH-UPDATE] 开始批量更新: ${updates.size}个操作")

            // 🔥 逐个应用更新，记录每步的中间状态以便回滚
            updates.forEachIndexed { index, update ->
                try {
                    val previousJson = currentJson
                    
                    currentJson = when (update.type) {
                        SessionUpdateType.STATUS -> {
                            val status = update.statusValue ?: com.example.gymbro.shared.models.workout.SessionStatus.ACTIVE
                            Timber.d("🔥 [SESSION-BATCH-UPDATE] 更新${index + 1}: 状态 = $status")
                            updateSessionStatus(currentJson, status)
                        }
                        SessionUpdateType.COUNTDOWN -> {
                            val countdown = update.countdownValue
                            Timber.d("🔥 [SESSION-BATCH-UPDATE] 更新${index + 1}: 倒计时 = $countdown")
                            updateSessionCountdown(currentJson, countdown)
                        }
                        SessionUpdateType.EXERCISE_SET -> {
                            val exerciseId = update.exerciseId ?: ""
                            val setOrderNo = update.setOrderNo ?: 0
                            Timber.d("🔥 [SESSION-BATCH-UPDATE] 更新${index + 1}: 动作组 $exerciseId-$setOrderNo")
                            updateSessionExerciseSet(
                                currentJson,
                                exerciseId,
                                setOrderNo,
                                update.weightValue,
                                update.repsValue,
                                update.completedAtValue,
                                update.restTimeValue
                            )
                        }
                    }

                    // 🔥 验证更新后的JSON有效性
                    if (currentJson == previousJson) {
                        Timber.w("🔥 [SESSION-BATCH-UPDATE] ⚠️ 更新${index + 1}未产生变化，可能操作无效")
                    }
                    
                    processedUpdates.add("${update.type}:${update.exerciseId ?: "session"}")
                } catch (e: Exception) {
                    Timber.e(e, "🔥 [SESSION-BATCH-UPDATE] ❌ 更新${index + 1}失败: ${update.type}")
                    // 🔥 单个更新失败时继续处理其他更新，而不是整体回滚
                    // 这确保了部分成功的更新不会丢失
                }
            }

            // 🔥 最终验证：确保处理后的JSON仍然有效
            try {
                json.decodeFromString<com.example.gymbro.shared.models.workout.WorkoutSessionDto>(currentJson)
                Timber.d("🔥 [SESSION-BATCH-UPDATE] ✅ 批量更新成功: ${processedUpdates.size}/${updates.size}个操作")
            } catch (e: Exception) {
                Timber.e(e, "🔥 [SESSION-BATCH-UPDATE] ❌ 最终JSON验证失败，回滚到原始状态")
                return originalJson
            }

            currentJson
        } catch (e: Exception) {
            Timber.e(e, "🔥 [SESSION-BATCH-UPDATE] ❌ 批量更新严重失败: ${updates.size}个更新")
            // 🔥 容错处理：返回原始数据确保数据不丢失
            jsonString
        }
    }

    /**
     * WorkoutSessionDto → JSON 字符串（标准化方法）
     * 🔥 标准化功能：提供与ExerciseJsonProcessor一致的转换接口
     */
    fun WorkoutSessionDto.toJson(): String {
        return try {
            val jsonString = json.encodeToString(this)

            // 验证序列化结果
            if (jsonString.length > JsonConstants.MAX_JSON_SIZE) {
                Timber.w("Session JSON 过大: ${jsonString.length} 字符")
            }

            jsonString
        } catch (e: Exception) {
            Timber.e(e, "Session 序列化失败: ${this.id}")
            createSessionFallbackJson(this.id, "Session_${this.templateId}")
        }
    }

    /**
     * 验证 Session JSON 的完整性（增强版）
     * 🔥 标准化功能：提供完整的数据验证，确保Session数据的有效性
     */
    fun validateSessionJsonEnhanced(jsonString: String): Boolean {
        return try {
            val sessionDto = json.decodeFromString<com.example.gymbro.shared.models.workout.WorkoutSessionDto>(jsonString)
            
            // 基础验证
            val basicValid = sessionDto.id.isNotBlank() && 
                           sessionDto.templateId.isNotBlank() &&
                           sessionDto.startAt > 0
            
            // 深度验证：检查exercises数据完整性
            val exercisesValid = sessionDto.exercises.all { exercise ->
                exercise.exerciseId.isNotBlank() && exercise.sets.all { set ->
                    set.orderNo > 0 && set.weight >= 0f && set.reps >= 0
                }
            }
            
            val isValid = basicValid && exercisesValid
            
            if (!isValid) {
                Timber.w("Session JSON 验证失败: basicValid=$basicValid, exercisesValid=$exercisesValid")
            }
            
            isValid
        } catch (e: Exception) {
            Timber.e(e, "Session JSON 验证失败")
            false
        }
    }
}

/**
 * Session 组数据
 */
data class SessionSetData(
    val id: String,
    val weight: Float? = null,
    val reps: Int? = null,
    val isCompleted: Boolean = false,
    val completedAt: Long? = null,
)

/**
 * Session 动作数据
 */
data class SessionExerciseData(
    val exerciseId: String,
    val exerciseName: String,
    val imageUrl: String? = null,
    val videoUrl: String? = null,
    val sets: List<SessionSetData>,
    val restTimeSeconds: Int = JsonConstants.Defaults.DEFAULT_REST_TIME,
    val notes: String? = null,
)

/**
 * Session 动作更新数据
 * 用于 Session 模式下的动作数据更新
 */
data class SessionExerciseUpdateData(
    val exerciseId: String,
    val sets: List<SessionSetData> = emptyList(),
    val restTimeSeconds: Int = JsonConstants.Defaults.DEFAULT_REST_TIME,
    val notes: String? = null,
)

/**
 * Session 数据更新类型（新增标准化枚举）
 * 🔥 标准化功能：定义Session可更新的数据类型
 */
enum class SessionUpdateType {
    STATUS,        // 会话状态更新
    COUNTDOWN,     // 倒计时更新
    EXERCISE_SET   // 动作组数据更新
}

/**
 * Session 数据更新数据类（新增标准化结构）
 * 🔥 标准化功能：统一的Session数据更新载体，支持批量操作
 */
data class SessionUpdateData(
    val type: SessionUpdateType,
    
    // Session级别更新字段
    val statusValue: com.example.gymbro.shared.models.workout.SessionStatus? = null,
    val countdownValue: Long? = null,
    
    // Exercise级别更新字段
    val exerciseId: String? = null,
    val setOrderNo: Int? = null,
    val weightValue: Float? = null,
    val repsValue: Int? = null,
    val completedAtValue: Long? = null,
    val restTimeValue: Int? = null,
)
