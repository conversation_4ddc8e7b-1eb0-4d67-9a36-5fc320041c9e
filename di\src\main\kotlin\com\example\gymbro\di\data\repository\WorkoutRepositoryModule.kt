package com.example.gymbro.di.data.repository

// Exercise相关Repository已移除，由独立的Exercise-Library模块管理
import com.example.gymbro.data.exercise.repository.ExerciseRepositoryImpl
import com.example.gymbro.data.workout.plan.repository.PlanRepositoryImpl
import com.example.gymbro.data.workout.repository.AnalysisStreamRepositoryImpl
import com.example.gymbro.data.workout.repository.StatsRepositoryImpl
import com.example.gymbro.data.workout.session.repository.SessionRepositoryImpl
import com.example.gymbro.data.workout.template.repository.TemplateRepositoryImpl
import com.example.gymbro.domain.exercise.repository.ExerciseRepository
import com.example.gymbro.domain.workout.repository.AnalysisStreamRepository
import com.example.gymbro.domain.workout.repository.PlanRepository
import com.example.gymbro.domain.workout.repository.SessionRepository
import com.example.gymbro.domain.workout.repository.StatsRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Workout Repository 依赖注入模块
 *
 * 基于四数据库架构设计 (05_Final_Database_Architecture.md)
 * 负责绑定四个数据库对应的 Repository 接口实现
 *
 * 四数据库架构：
 * - ExerciseDB: 基础动作库管理
 * - TemplateDB: 训练模板管理
 * - PlanDB: 训练计划管理
 * - SessionDB: 训练记录与统计
 *
 * P2阶段：基础Repository接口重设计
 * - 删除旧的不匹配接口
 * - 创建与四数据库实现完全匹配的新接口
 * - 确保接口与实现方法签名一致
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class WorkoutRepositoryModule {

    // ==================== ExerciseDB Repository ====================

    /**
     * 绑定 ExerciseRepository 接口实现
     * 负责基础动作库管理
     */
    @Binds
    @Singleton
    abstract fun bindExerciseRepository(
        impl: ExerciseRepositoryImpl,
    ): ExerciseRepository

    // ==================== TemplateDB Repository ====================

    /**
     * 绑定 TemplateRepository 接口实现
     * 负责训练模板管理
     */
    @Binds
    @Singleton
    abstract fun bindTemplateRepository(
        impl: TemplateRepositoryImpl,
    ): TemplateRepository

    // ==================== PlanDB Repository ====================

    /**
     * 绑定 PlanRepository 接口实现
     * 负责训练计划管理
     */
    @Binds
    @Singleton
    abstract fun bindPlanRepository(
        impl: PlanRepositoryImpl,
    ): PlanRepository

    // ==================== SessionDB Repository ====================

    /**
     * 绑定 SessionRepository 接口实现
     * 负责训练记录与统计，包含自动保存功能
     */
    @Binds
    @Singleton
    abstract fun bindSessionRepository(
        impl: SessionRepositoryImpl,
    ): SessionRepository

    // ==================== StatsDB Repository ====================

    /**
     * 绑定 StatsRepository 接口实现
     * 负责训练统计数据管理和分析
     */
    @Binds
    @Singleton
    abstract fun bindStatsRepository(
        impl: StatsRepositoryImpl,
    ): StatsRepository

    // ==================== AnalysisStreamDB Repository ====================

    /**
     * 绑定 AnalysisStreamRepository 接口实现
     * 负责AI训练分析的流式响应处理，支持事件总线架构
     */
    @Binds
    @Singleton
    abstract fun bindAnalysisStreamRepository(
        impl: AnalysisStreamRepositoryImpl,
    ): AnalysisStreamRepository

    // ==================== JSON处理器绑定 ====================
    // 注意：JsonProcessorPort绑定已移至 JsonProcessorModule
    // 避免重复绑定，使用统一的UnifiedJsonProcessorImpl实现

}
