package com.example.gymbro.features.workout.session

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.FitnessCenter
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.features.workout.session.internal.components.*
import kotlinx.coroutines.delay
import timber.log.Timber

/**
 * 全屏沉浸式训练会话界面
 *
 * 核心特性：
 * - 全屏沉浸体验，隐藏系统状态栏和导航栏
 * - 双击屏幕显示/隐藏TopBar
 * - 集成AI助手小窗功能
 * - 滚动位置自动保存和恢复
 * - 全局覆盖层支持（倒计时、AI窗口等）
 * - 完整的MVI 2.0架构
 * - 生命周期感知的状态恢复
 *
 * @param sessionId 训练会话ID
 * @param onNavigateBack 返回导航回调
 * @param onNavigateToSummary 导航到训练总结回调
 * @param viewModel SessionViewModel实例
 */

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun SessionScreen(
    sessionId: String,
    onNavigateBack: () -> Unit,
    onNavigateToSummary: (String) -> Unit = {},
    viewModel: SessionViewModel = hiltViewModel(),
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val view = LocalView.current
    rememberCoroutineScope()

    // TopBar显示状态

    // 滚动状态 - 使用SavedStateHandle保存
    val listState =
        rememberLazyListState(
            initialFirstVisibleItemIndex = state.scrollState.firstVisibleItemIndex,
            initialFirstVisibleItemScrollOffset = state.scrollState.firstVisibleItemScrollOffset,
        )

    // 沉浸式模式设置
    DisposableEffect(Unit) {
        val window = (context as androidx.activity.ComponentActivity).window
        val windowInsetsController = WindowCompat.getInsetsController(window, view)

        // 启用沉浸式模式
        windowInsetsController.apply {
            hide(WindowInsetsCompat.Type.statusBars() or WindowInsetsCompat.Type.navigationBars())
            systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }

        Timber.d("WorkoutSession: 启用沉浸式模式")

        onDispose {
            // 恢复系统栏显示
            windowInsetsController.show(
                WindowInsetsCompat.Type.statusBars() or WindowInsetsCompat.Type.navigationBars(),
            )
            Timber.d("WorkoutSession: 恢复系统栏显示")
        }
    }

    // MVI Effect处理
    LaunchedEffect(Unit) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is SessionContract.Effect.NavigateBack -> {
                    onNavigateBack()
                }
                is SessionContract.Effect.NavigateToWorkoutSummary -> {
                    onNavigateToSummary(effect.summary.toString())
                }
                is SessionContract.Effect.ShowSnackbar -> {
                    // 简单日志记录，不实现复杂的Snackbar
                    Timber.tag("SessionScreen").d("Snackbar: ${effect.message}")
                }
                is SessionContract.Effect.HapticFeedback -> {
                    // 触觉反馈功能已移除
                }
                is SessionContract.Effect.KeepScreenOn -> {
                    // 屏幕常亮功能已移除
                }
                is SessionContract.Effect.ReleaseScreenLock -> {
                    // 屏幕锁定功能已移除
                }
                else -> {
                    // 其他Effect处理
                }
            }
        }
    }

    // 初始化会话
    LaunchedEffect(sessionId) {
        if (sessionId.isNotEmpty()) {
            Timber.d("SessionScreen: 加载现有会话 sessionId=$sessionId")
            viewModel.dispatch(SessionContract.Intent.LoadSession(sessionId))
        } else {
            // 🔥 移除：训练源选择器逻辑已迁移到 SessionTemplateLoadingScreen
            Timber.w("SessionScreen: sessionId 为空，应该使用 SessionTemplateLoadingScreen 进行模板选择")
        }
    }

    // 🔥 已移除：滚动位置保存注释 - 自动保存功能已被移除
    LaunchedEffect(listState.firstVisibleItemIndex, listState.firstVisibleItemScrollOffset) {
        // 🔥 已移除：AutoSave系统注释 - 自动保存功能已被移除
        delay(1000)
        viewModel.dispatch(
            SessionContract.Intent.SaveScrollPosition(),
        )
    }

    // 返回键处理
    BackHandler {
        if (state.hasUnsavedChanges) {
            // 显示确认对话框 - 简化实现，直接询问用户
            viewModel.dispatch(SessionContract.Intent.ShowCompleteWorkoutDialog)
        } else {
            onNavigateBack()
        }
    }

    // 主要内容容器 - 重构为纯容器架构
    Box(
        modifier =
        Modifier
            .fillMaxSize()
            .background(MaterialTheme.workoutColors.cardBackground),
    ) {
        // Session内容区域
        Column(
            modifier = Modifier.fillMaxSize(),
        ) {
            // 顶部区域组件
            SessionHeaderComponent(
                sessionState = state,
                onBackClick = onNavigateBack,
                onTimerToggle = { viewModel.dispatch(SessionContract.Intent.ToggleTimer) },
                onAiAssistantClick = {
                    // AI助手现在由AiAssistantCard自己管理，无需处理
                },
                modifier = Modifier.fillMaxWidth(),
            )

            // 主内容区域 - 根据状态显示内容、训练源选择器或空状态
            when {
                // 🔥 移除：训练源选择器逻辑已迁移到 SessionTemplateLoadingScreen
                // 有训练数据或正在加载时显示正常内容
                state.exercises.isNotEmpty() || state.isLoading -> {
                    // 有训练数据或正在加载时显示正常内容
                    SessionContentArea(
                        exercises = state.exercises,
                        currentExerciseIndex = state.currentExerciseIndex,
                        onExerciseUpdate = { exerciseId, updateData ->
                            viewModel.dispatch(SessionContract.Intent.UpdateExercise(exerciseId, updateData))
                        },
                        onUpdateExerciseWithSets = { exerciseId, sessionUpdateData ->
                            // 🔥 关键修复：使用新的 UpdateExerciseWithSets Intent 支持完整 sets 数据更新
                            viewModel.dispatch(
                                SessionContract.Intent.UpdateExerciseWithSets(exerciseId, sessionUpdateData),
                            )
                            timber.log.Timber.d(
                                "🔧 [SessionScreen] 派发 UpdateExerciseWithSets Intent: 动作=$exerciseId, 组数=${sessionUpdateData.sets.size}",
                            )
                        },
                        onSetComplete = { exerciseId, setId ->
                            viewModel.dispatch(SessionContract.Intent.CompleteSet(exerciseId, setId))
                        },
                        onAddSet = { exerciseId ->
                            // 将exerciseId转换为exerciseIndex
                            val exerciseIndex = state.exercises.indexOfFirst { it.exerciseId == exerciseId }
                            if (exerciseIndex >= 0) {
                                viewModel.dispatch(SessionContract.Intent.AddSet(exerciseIndex))
                            }
                        },
                        onRestTimerStart = { restSeconds ->
                            // 使用全局悬浮倒计时系统，不再需要发送Intent
                        },
                        onExerciseReview = { exerciseId ->
                            viewModel.dispatch(SessionContract.Intent.ReviewExercise(exerciseId))
                        },
                        onExercisePreview = { exerciseId ->
                            viewModel.dispatch(SessionContract.Intent.PreviewExercise(exerciseId))
                        },
                        // 🔥 移除：Keypad 逻辑已移至 WorkoutExerciseComponent 内部处理
                        modifier = Modifier.weight(1f),
                    )
                }
                else -> {
                    // 🔥 修改：空状态时导航回去，让用户通过 SessionTemplateLoadingScreen 选择模板
                    EmptySessionState(
                        onSelectSource = {
                            // 导航回去，让用户重新选择模板
                            onNavigateBack()
                        },
                        modifier = Modifier.weight(1f),
                    )
                }
            }

            // 底部统计组件
            SessionStatisticsComponent(
                statistics = state.toSessionStatistics(),
                onStatisticClick = { statType ->
                    viewModel.dispatch(SessionContract.Intent.ViewStatisticDetail(statType))
                },
                modifier = Modifier.fillMaxWidth(),
            )
        }

        // AI助手卡片 - 使用更完整的AiAssistantCard实现
        AiAssistantCard(
            sessionId = state.sessionId,
            modifier =
            Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = 90.dp, end = 16.dp),
            // 避开倒计时悬浮窗
        )

        // 完成训练对话框
        if (state.showCompleteWorkoutDialog) {
            CompletionDialog(
                onConfirm = {
                    // 实际完成Session，这会触发Plan progress更新
                    viewModel.dispatch(SessionContract.Intent.CompleteSession)
                    viewModel.dispatch(SessionContract.Intent.HideCompleteWorkoutDialog)
                },
                onDismiss = {
                    viewModel.dispatch(SessionContract.Intent.HideCompleteWorkoutDialog)
                },
            )
        }

        // 休息计时器功能已迁移到全局悬浮倒计时系统

        // 动作选择器功能已移除 - 专注核心训练流程
    }
}

// 旧的WorkoutContent已被SessionContentArea替代，移除此函数

// 旧的WorkoutProgressHeader已被SessionHeaderComponent替代，移除此函数

// 旧的WorkoutImmersiveBar已被SessionHeaderComponent替代，移除此函数

/**
 * 预览组件 - 重构后的SessionScreen
 */
@GymBroPreview
@Composable
private fun SessionScreenRefactoredPreview() {
    GymBroTheme {
        // 展示重构后的完整Session页面
        // 使用纯容器架构：SessionHeaderComponent + SessionContentArea + SessionStatisticsComponent
        SessionScreen(
            sessionId = "preview_session",
            onNavigateBack = {},
            onNavigateToSummary = {},
        )
    }
}

/**
 * 空状态组件 - 引导用户选择训练源
 */
@Composable
private fun EmptySessionState(
    onSelectSource: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier =
        modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        // 图标
        Icon(
            imageVector = Icons.Default.FitnessCenter,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.6f),
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 标题
        Text(
            text = "开始训练",
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.workoutColors.aiCoachText,
        )

        Spacer(modifier = Modifier.height(8.dp))

        // 描述
        Text(
            text = "选择一个训练模板或计划开始你的训练",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.workoutColors.accentSecondary,
            textAlign = TextAlign.Center,
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 选择按钮
        Button(
            onClick = onSelectSource,
            modifier = Modifier.fillMaxWidth(0.6f),
        ) {
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = null,
                modifier = Modifier.size(18.dp),
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("选择训练源")
        }
    }
}

/**
 * 预览组件 - 原始版本（保持向后兼容）
 */
@GymBroPreview
@Composable
private fun SessionScreenPreview() {
    GymBroTheme {
        SessionScreen(
            sessionId = "preview_session",
            onNavigateBack = {},
            onNavigateToSummary = {},
        )
    }
}
