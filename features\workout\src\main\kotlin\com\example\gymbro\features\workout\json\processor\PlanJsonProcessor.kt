package com.example.gymbro.features.workout.json.processor

import com.example.gymbro.domain.workout.model.WorkoutPlan
import com.example.gymbro.domain.workout.model.plan.DayPlan
import com.example.gymbro.features.workout.json.core.JsonConstants
import com.example.gymbro.shared.models.workout.PlanCalendarData
import com.example.gymbro.shared.models.workout.PlanCalendarInfo
import com.example.gymbro.shared.models.workout.CalendarEntryData
import com.example.gymbro.shared.models.workout.PlanFunctionCallData
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber

/**
 * Plan JSON 处理器 v1.0 - 全功能标准化版本
 *
 * 🎯 核心功能：基于ExerciseJsonProcessor标准化模式
 * - WorkoutPlan数据的JSON序列化/反序列化
 * - PlanCalendarPayload的完整支持
 * - 动态字段修改功能，支持实时编辑
 * - 事务性批量更新，确保数据一致性
 * - 强化错误处理和容错机制
 * - 提供完整的CRUD操作集
 *
 * 功能特性：
 * - WorkoutPlan ↔ JSON String 转换
 * - 计划日历数据生成和管理
 * - 动态字段修改（计划名称、描述、调度等）
 * - 批量更新操作，支持复杂的计划修改
 * - 数据完整性验证和回滚机制
 * - 丰富的调试日志和错误处理
 *
 * <AUTHOR> AI Assistant
 * @since 1.0.0 (标准化创建版本)
 */
object PlanJsonProcessor {

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        isLenient = true
        prettyPrint = false
    }

    // ==================== 核心转换方法 ====================

    /**
     * WorkoutPlan → JSON 字符串
     * 🔥 标准化功能：提供与ExerciseJsonProcessor一致的转换接口
     */
    fun WorkoutPlan.toJson(): String {
        return try {
            val jsonString = json.encodeToString(this)

            // 验证序列化结果
            if (jsonString.length > JsonConstants.MAX_JSON_SIZE) {
                Timber.w("Plan JSON 过大: ${jsonString.length} 字符")
            }

            jsonString
        } catch (e: Exception) {
            Timber.e(e, "Plan 序列化失败: ${this.name}")
            createFallbackPlanJson(this)
        }
    }

    /**
     * JSON 字符串 → WorkoutPlan
     */
    fun fromJson(jsonString: String): WorkoutPlan? {
        return try {
            val plan = json.decodeFromString<WorkoutPlan>(jsonString)
            plan
        } catch (e: Exception) {
            Timber.e(e, "Plan 反序列化失败")
            null
        }
    }

    /**
     * 批量转换 WorkoutPlan 列表 → JSON 数组字符串
     */
    fun List<WorkoutPlan>.toJsonArray(): String {
        return try {
            json.encodeToString(this)
        } catch (e: Exception) {
            Timber.e(e, "Plan 列表序列化失败")
            "[]"
        }
    }

    /**
     * JSON 数组字符串 → WorkoutPlan 列表
     */
    fun fromJsonArray(jsonString: String): List<WorkoutPlan> {
        return try {
            json.decodeFromString<List<WorkoutPlan>>(jsonString)
        } catch (e: Exception) {
            Timber.e(e, "Plan 列表反序列化失败")
            emptyList()
        }
    }

    // ==================== 日历数据处理方法 ====================

    /**
     * WorkoutPlan → PlanCalendarData 转换
     * 🔥 标准化功能：专门处理计划的日历数据生成
     */
    fun generateCalendarData(plan: WorkoutPlan, startDate: String): PlanCalendarData {
        return try {
            Timber.d("🗓️ [CALENDAR-GENERATE] 生成计划日历数据: ${plan.name}, 开始日期: $startDate")
            
            val calendarData = plan.toCalendarJson(startDate)
            
            Timber.d("🗓️ [CALENDAR-GENERATE] 成功生成日历数据: ${calendarData.calendarEntries.size}个条目")
            
            calendarData
        } catch (e: Exception) {
            Timber.e(e, "生成计划日历数据失败")
            createFallbackCalendarData(plan, startDate)
        }
    }

    /**
     * PlanCalendarData → JSON 字符串
     */
    fun PlanCalendarData.toJson(): String {
        return try {
            json.encodeToString(this)
        } catch (e: Exception) {
            Timber.e(e, "日历数据序列化失败")
            "{}"
        }
    }

    /**
     * JSON 字符串 → PlanCalendarData
     */
    fun calendarFromJson(jsonString: String): PlanCalendarData? {
        return try {
            json.decodeFromString<PlanCalendarData>(jsonString)
        } catch (e: Exception) {
            Timber.e(e, "日历数据反序列化失败")
            null
        }
    }

    // ==================== 动态数据修改方法 ====================

    /**
     * 更新 Plan 的名称
     * 🔥 标准化功能：动态修改计划名称，支持实时编辑
     */
    fun updatePlanName(jsonString: String, newName: String): String {
        return try {
            val plan = fromJson(jsonString) ?: return jsonString
            
            Timber.d("🔧 [PLAN-NAME-UPDATE] 开始更新计划名称: ${plan.id} \"${plan.name}\" -> \"$newName\"")
            
            val updatedPlan = plan.copy(
                name = com.example.gymbro.core.ui.text.UiText.DynamicString(newName),
                updatedAt = System.currentTimeMillis()
            )
            
            val result = updatedPlan.toJson()
            
            // 🔥 验证更新结果
            val verifyPlan = fromJson(result)
            Timber.d("🔧 [PLAN-NAME-UPDATE] 验证结果: 计划${verifyPlan?.id}的名称为\"${verifyPlan?.name}\"")
            
            result
        } catch (e: Exception) {
            Timber.e(e, "更新计划名称失败: $newName")
            jsonString
        }
    }

    /**
     * 更新 Plan 的描述
     * 🔥 标准化功能：动态修改计划描述
     */
    fun updatePlanDescription(jsonString: String, newDescription: String?): String {
        return try {
            val plan = fromJson(jsonString) ?: return jsonString
            
            Timber.d("🔧 [PLAN-DESC-UPDATE] 更新计划描述: ${plan.id}")
            
            val updatedPlan = plan.copy(
                description = newDescription?.let { com.example.gymbro.core.ui.text.UiText.DynamicString(it) },
                updatedAt = System.currentTimeMillis()
            )
            
            updatedPlan.toJson()
        } catch (e: Exception) {
            Timber.e(e, "更新计划描述失败")
            jsonString
        }
    }

    /**
     * 更新 Plan 的总天数
     * 🔥 标准化功能：动态修改计划总天数，自动调整日程安排
     */
    fun updatePlanTotalDays(jsonString: String, newTotalDays: Int): String {
        return try {
            val plan = fromJson(jsonString) ?: return jsonString
            
            Timber.d("🔧 [PLAN-DAYS-UPDATE] 更新计划天数: ${plan.id} ${plan.totalDays} -> $newTotalDays")
            
            // 调整 dailySchedule 以匹配新的总天数
            val adjustedSchedule = when {
                newTotalDays > plan.totalDays -> {
                    // 增加天数：为新天数创建休息日
                    val existingSchedule = plan.dailySchedule.toMutableMap()
                    for (day in (plan.totalDays + 1)..newTotalDays) {
                        existingSchedule[day] = DayPlan.createRestDay(day)
                    }
                    existingSchedule
                }
                newTotalDays < plan.totalDays -> {
                    // 减少天数：移除超出的天数
                    plan.dailySchedule.filterKeys { it <= newTotalDays }
                }
                else -> plan.dailySchedule
            }
            
            val updatedPlan = plan.copy(
                totalDays = newTotalDays,
                dailySchedule = adjustedSchedule,
                updatedAt = System.currentTimeMillis()
            )
            
            updatedPlan.toJson()
        } catch (e: Exception) {
            Timber.e(e, "更新计划天数失败: $newTotalDays")
            jsonString
        }
    }

    /**
     * 更新指定天的 DayPlan
     * 🔥 标准化功能：动态修改特定天的训练安排
     */
    fun updateDayPlan(jsonString: String, dayNumber: Int, newDayPlan: DayPlan): String {
        return try {
            val plan = fromJson(jsonString) ?: return jsonString
            
            Timber.d("🔧 [DAY-PLAN-UPDATE] 更新第${dayNumber}天计划: ${plan.id}")
            
            val updatedSchedule = plan.dailySchedule.toMutableMap()
            updatedSchedule[dayNumber] = newDayPlan
            
            val updatedPlan = plan.copy(
                dailySchedule = updatedSchedule,
                updatedAt = System.currentTimeMillis()
            )
            
            updatedPlan.toJson()
        } catch (e: Exception) {
            Timber.e(e, "更新第${dayNumber}天计划失败")
            jsonString
        }
    }

    /**
     * 为指定天添加训练模板
     * 🔥 标准化功能：向特定天添加新的训练模板
     */
    fun addTemplateToDay(jsonString: String, dayNumber: Int, templateId: String): String {
        return try {
            val plan = fromJson(jsonString) ?: return jsonString
            
            Timber.d("🔧 [ADD-TEMPLATE] 向第${dayNumber}天添加模板: $templateId")
            
            val currentDayPlan = plan.getDayPlan(dayNumber) ?: DayPlan.createRestDay(dayNumber)
            val updatedTemplateIds = currentDayPlan.templateVersionIds + templateId
            
            val updatedDayPlan = currentDayPlan.copy(
                templateVersionIds = updatedTemplateIds,
                isRestDay = false // 添加训练后不再是休息日
            )
            
            updateDayPlan(jsonString, dayNumber, updatedDayPlan)
        } catch (e: Exception) {
            Timber.e(e, "向第${dayNumber}天添加模板失败: $templateId")
            jsonString
        }
    }

    /**
     * 从指定天移除训练模板
     * 🔥 标准化功能：从特定天移除训练模板
     */
    fun removeTemplateFromDay(jsonString: String, dayNumber: Int, templateId: String): String {
        return try {
            val plan = fromJson(jsonString) ?: return jsonString
            
            Timber.d("🔧 [REMOVE-TEMPLATE] 从第${dayNumber}天移除模板: $templateId")
            
            val currentDayPlan = plan.getDayPlan(dayNumber) ?: return jsonString
            val updatedTemplateIds = currentDayPlan.templateVersionIds.filter { it != templateId }
            
            val updatedDayPlan = currentDayPlan.copy(
                templateVersionIds = updatedTemplateIds,
                isRestDay = updatedTemplateIds.isEmpty() // 如果没有训练则设为休息日
            )
            
            updateDayPlan(jsonString, dayNumber, updatedDayPlan)
        } catch (e: Exception) {
            Timber.e(e, "从第${dayNumber}天移除模板失败: $templateId")
            jsonString
        }
    }

    // ==================== 批量更新方法 ====================

    /**
     * 批量更新 Plan 数据 - 事务性处理版本
     * 🔥 核心标准化功能：基于ExerciseJsonProcessor模式的批量更新
     */
    fun batchUpdatePlan(jsonString: String, updates: List<PlanUpdateData>): String {
        if (updates.isEmpty()) {
            Timber.d("🔥 [PLAN-BATCH-UPDATE] 批量更新列表为空，返回原始数据")
            return jsonString
        }

        return try {
            // 🔥 事务性处理：先验证所有更新操作的有效性
            val originalJson = jsonString
            var currentJson = originalJson
            val processedUpdates = mutableListOf<String>()

            Timber.d("🔥 [PLAN-BATCH-UPDATE] 开始批量更新: ${updates.size}个操作")

            // 🔥 逐个应用更新，记录每步的中间状态以便回滚
            updates.forEachIndexed { index, update ->
                try {
                    val previousJson = currentJson
                    
                    currentJson = when (update.type) {
                        PlanUpdateType.NAME -> {
                            val name = update.nameValue ?: ""
                            Timber.d("🔥 [PLAN-BATCH-UPDATE] 更新${index + 1}: 名称 = \"$name\"")
                            updatePlanName(currentJson, name)
                        }
                        PlanUpdateType.DESCRIPTION -> {
                            val description = update.descriptionValue
                            Timber.d("🔥 [PLAN-BATCH-UPDATE] 更新${index + 1}: 描述 = \"$description\"")
                            updatePlanDescription(currentJson, description)
                        }
                        PlanUpdateType.TOTAL_DAYS -> {
                            val totalDays = update.totalDaysValue ?: 0
                            Timber.d("🔥 [PLAN-BATCH-UPDATE] 更新${index + 1}: 总天数 = $totalDays")
                            updatePlanTotalDays(currentJson, totalDays)
                        }
                        PlanUpdateType.DAY_PLAN -> {
                            val dayNumber = update.dayNumber ?: 0
                            val dayPlan = update.dayPlanValue
                            if (dayPlan != null) {
                                Timber.d("🔥 [PLAN-BATCH-UPDATE] 更新${index + 1}: 第${dayNumber}天计划")
                                updateDayPlan(currentJson, dayNumber, dayPlan)
                            } else currentJson
                        }
                        PlanUpdateType.ADD_TEMPLATE -> {
                            val dayNumber = update.dayNumber ?: 0
                            val templateId = update.templateIdValue ?: ""
                            Timber.d("🔥 [PLAN-BATCH-UPDATE] 更新${index + 1}: 添加模板 $templateId 到第${dayNumber}天")
                            addTemplateToDay(currentJson, dayNumber, templateId)
                        }
                        PlanUpdateType.REMOVE_TEMPLATE -> {
                            val dayNumber = update.dayNumber ?: 0
                            val templateId = update.templateIdValue ?: ""
                            Timber.d("🔥 [PLAN-BATCH-UPDATE] 更新${index + 1}: 从第${dayNumber}天移除模板 $templateId")
                            removeTemplateFromDay(currentJson, dayNumber, templateId)
                        }
                    }

                    // 🔥 验证更新后的JSON有效性
                    if (currentJson == previousJson) {
                        Timber.w("🔥 [PLAN-BATCH-UPDATE] ⚠️ 更新${index + 1}未产生变化，可能操作无效")
                    }
                    
                    processedUpdates.add("${update.type}:${update.dayNumber ?: "plan"}")
                } catch (e: Exception) {
                    Timber.e(e, "🔥 [PLAN-BATCH-UPDATE] ❌ 更新${index + 1}失败: ${update.type}")
                    // 🔥 单个更新失败时继续处理其他更新，而不是整体回滚
                    // 这确保了部分成功的更新不会丢失
                }
            }

            // 🔥 最终验证：确保处理后的JSON仍然有效
            try {
                json.decodeFromString<WorkoutPlan>(currentJson)
                Timber.d("🔥 [PLAN-BATCH-UPDATE] ✅ 批量更新成功: ${processedUpdates.size}/${updates.size}个操作")
            } catch (e: Exception) {
                Timber.e(e, "🔥 [PLAN-BATCH-UPDATE] ❌ 最终JSON验证失败，回滚到原始状态")
                return originalJson
            }

            currentJson
        } catch (e: Exception) {
            Timber.e(e, "🔥 [PLAN-BATCH-UPDATE] ❌ 批量更新严重失败: ${updates.size}个更新")
            // 🔥 容错处理：返回原始数据确保数据不丢失
            jsonString
        }
    }

    // ==================== 容错处理方法 ====================

    /**
     * 创建 Plan 的 fallback JSON
     */
    private fun createFallbackPlanJson(plan: WorkoutPlan): String {
        return try {
            val fallbackPlan = WorkoutPlan(
                id = plan.id.takeIf { it.isNotBlank() } ?: "fallback_plan",
                name = com.example.gymbro.core.ui.text.UiText.DynamicString("未知计划"),
                userId = plan.userId.takeIf { it.isNotBlank() } ?: "unknown_user",
                totalDays = 7,
                dailySchedule = mapOf(1 to DayPlan.createRestDay(1))
            )

            json.encodeToString(fallbackPlan)
        } catch (e: Exception) {
            Timber.e(e, "创建 fallback Plan JSON 失败")
            "{\"id\":\"fallback_plan\",\"name\":{\"text\":\"未知计划\"},\"userId\":\"unknown_user\",\"totalDays\":7,\"dailySchedule\":{}}"
        }
    }

    /**
     * 创建 fallback 日历数据
     */
    private fun createFallbackCalendarData(plan: WorkoutPlan, startDate: String): PlanCalendarData {
        return try {
            val planInfo = PlanCalendarInfo(
                planId = plan.id,
                planName = plan.name.toString(),
                description = "数据恢复模式",
                totalDays = 1,
                workoutDays = 0,
                restDays = 1,
                createdAt = plan.createdAt,
                updatedAt = System.currentTimeMillis()
            )

            val calendarEntries = listOf(
                CalendarEntryData(
                    date = startDate,
                    dayNumber = 1,
                    isRestDay = true,
                    templateIds = emptyList(),
                    workoutCount = 0,
                    notes = "数据恢复模式"
                )
            )

            PlanCalendarData(
                planInfo = planInfo,
                calendarEntries = calendarEntries
            )
        } catch (e: Exception) {
            Timber.e(e, "创建 fallback 日历数据失败")
            // 返回最简单的空数据结构
            PlanCalendarData(
                planInfo = PlanCalendarInfo(
                    planId = "fallback",
                    planName = "恢复模式",
                    totalDays = 0,
                    workoutDays = 0,
                    restDays = 0,
                    createdAt = 0,
                    updatedAt = 0
                ),
                calendarEntries = emptyList()
            )
        }
    }

    /**
     * 验证 Plan JSON 的完整性
     */
    fun validatePlanJson(jsonString: String): Boolean {
        return try {
            val plan = fromJson(jsonString)
            plan != null &&
                    plan.id.isNotBlank() &&
                    plan.name.toString().isNotBlank() &&
                    plan.userId.isNotBlank() &&
                    plan.totalDays > 0
        } catch (e: Exception) {
            Timber.w(e, "Plan JSON 验证失败")
            false
        }
    }

    /**
     * 验证日历数据的完整性
     */
    fun validateCalendarJson(jsonString: String): Boolean {
        return try {
            val calendarData = calendarFromJson(jsonString)
            calendarData != null &&
                    calendarData.planInfo.planId.isNotBlank() &&
                    calendarData.planInfo.planName.isNotBlank() &&
                    calendarData.calendarEntries.isNotEmpty()
        } catch (e: Exception) {
            Timber.w(e, "日历 JSON 验证失败")
            false
        }
    }
}

/**
 * Plan 数据更新类型（标准化枚举）
 * 🔥 标准化功能：定义Plan可更新的数据类型
 */
enum class PlanUpdateType {
    NAME,           // 计划名称更新
    DESCRIPTION,    // 计划描述更新
    TOTAL_DAYS,     // 总天数更新
    DAY_PLAN,       // 单天计划更新
    ADD_TEMPLATE,   // 添加训练模板
    REMOVE_TEMPLATE // 移除训练模板
}

/**
 * Plan 数据更新数据类（标准化结构）
 * 🔥 标准化功能：统一的Plan数据更新载体，支持批量操作
 */
data class PlanUpdateData(
    val type: PlanUpdateType,
    
    // Plan级别更新字段
    val nameValue: String? = null,
    val descriptionValue: String? = null,
    val totalDaysValue: Int? = null,
    
    // Day级别更新字段
    val dayNumber: Int? = null,
    val dayPlanValue: DayPlan? = null,
    val templateIdValue: String? = null,
)