package com.example.gymbro.core.network.protocol

import android.os.SystemClock
import com.example.gymbro.core.network.config.NetworkConfigManager
import com.example.gymbro.core.network.eventbus.TokenBus
import com.example.gymbro.core.network.eventbus.TokenEvent
import com.example.gymbro.core.network.router.TokenRouter
import com.example.gymbro.core.network.retry.ExponentialBackoffRetryStrategy
import com.example.gymbro.core.network.retry.NetworkRetryStrategy
import com.example.gymbro.core.network.security.StringXmlEscaper
import com.example.gymbro.core.network.state.ConnectionState
import com.example.gymbro.core.network.state.ConnectionQuality
import com.example.gymbro.core.network.state.StreamClientStateProvider
import com.example.gymbro.core.network.ws.LlmStreamClient
import com.example.gymbro.core.network.ws.WsState
import com.example.gymbro.shared.models.ai.ChatMessage
import com.example.gymbro.shared.models.ai.ChatRequest
import com.example.gymbro.shared.models.network.NetworkResult
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.sse.EventSource
import okhttp3.sse.EventSourceListener
import okhttp3.sse.EventSources
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 修复：删除重复的CoreChatMessage定义，使用shared-models中的ChatMessage
 */
@Serializable
data class ChatCompletionRequest(
    val model: String,
    val messages: List<ChatMessage>,
    val stream: Boolean = true,
)

/**
 * 自适应流式客户端 - 智能协议选择实现
 *
 * 🎯 协议策略：
 * 1. 默认HTTP+SSE - 主要协议，稳定可靠
 * 2. 降级HTTP基础 - SSE不支持时的备选方案
 * 3. WebSocket专用 - 仅用于热更新配置的指定模型
 * 4. 动态配置 - 从NetworkConfigManager获取API配置
 */
@Singleton
class AdaptiveStreamClient @Inject constructor(
    private val httpClient: OkHttpClient,
    private val json: Json,
    private val tokenBus: TokenBus,
    private val tokenRouter: TokenRouter, // 🔥 【726task修复】添加TokenRouter依赖
    private val networkConfigManager: NetworkConfigManager,
    private val protocolDetector: ProtocolDetector,
    private val stringXmlEscaper: StringXmlEscaper,
) : LlmStreamClient, StreamClientStateProvider {

    // 🔥 【连接状态管理】新增状态提供者实现
    private val stateProvider = DefaultStreamClientStateProvider()

    // 🔥 【重试策略】网络重试管理
    private val retryStrategy: NetworkRetryStrategy = ExponentialBackoffRetryStrategy(
        initialDelayMs = 1000L,
        maxDelayMs = 30000L,
        maxRetries = 5
    )

    // 🔥 【心跳检测】连接健康检查
    private var lastHeartbeatTime = 0L

    // 🔥 【协程作用域】用于处理异步操作
    private val handlerScope = MainScope()
    private val heartbeatIntervalMs = 30000L // 30秒心跳间隔

    // 代理StreamClientStateProvider方法
    override val connectionState = stateProvider.connectionState
    override fun notifyConnectionStateChange(state: ConnectionState) = stateProvider.notifyConnectionStateChange(state)
    override fun startStateMonitoring() = stateProvider.startStateMonitoring()
    override fun stopStateMonitoring() = stateProvider.stopStateMonitoring()

    // 🔥 【协议检测】枚举
    private enum class DetectedProtocol {
        UNKNOWN, JSON, TEXT
    }

    // 🔥 【协议检测】状态映射
    private val protocolMap = mutableMapOf<String, DetectedProtocol>()

    // 🔥 【上下文注入】解析状态管理
    private data class ParseState(
        var hasSentThinkTag: Boolean = false,
        var hasSentCloseThinkTag: Boolean = false,
    )

    private val parseStates = mutableMapOf<String, ParseState>()

    /**
     * 🔥 【上下文注入】解析JSON内容并注入XML上下文标签
     *
     * 集成 StringXmlEscaper 确保输出安全的 XML 格式
     */
    private fun parseJsonContentWithContext(sseData: String, messageId: String): String? {
        if (sseData.isBlank() || sseData == "[DONE]") {
            return null
        }

        return try {
            // 移除data:前缀
            val jsonStr = sseData.removePrefix("data:").trim()
            if (jsonStr.isEmpty() || jsonStr == "[DONE]") {
                return null
            }

            // 尝试解析JSON
            val jsonElement = json.parseToJsonElement(jsonStr)
            val jsonObject = jsonElement.jsonObject

            // 获取choices数组
            val choices = jsonObject["choices"]?.jsonArray
            val delta = choices?.firstOrNull()?.jsonObject?.get("delta")?.jsonObject

            if (delta == null) {
                return null
            }

            // 提取reasoning_content和content字段
            val reasoningContent = delta["reasoning_content"]?.jsonPrimitive?.contentOrNull
            val content = delta["content"]?.jsonPrimitive?.contentOrNull

            // 获取或创建解析状态
            val state = parseStates.getOrPut(messageId) { ParseState() }

            // 🔥 【上下文注入逻辑】动态构建XML上下文，使用 StringXmlEscaper 确保安全
            return when {
                !reasoningContent.isNullOrEmpty() -> {
                    // 🔥 使用 StringXmlEscaper 清理和转义内容
                    val cleanContent = stringXmlEscaper.sanitizeAndEscape(reasoningContent)

                    // reasoning_content: 注入<think>标签
                    if (!state.hasSentThinkTag) {
                        state.hasSentThinkTag = true
                        "<think>$cleanContent"
                    } else {
                        cleanContent
                    }
                }
                !content.isNullOrEmpty() -> {
                    // 🔥 使用 StringXmlEscaper 清理和转义内容
                    val cleanContent = stringXmlEscaper.sanitizeAndEscape(content)

                    // content: 注入</think>标签
                    if (state.hasSentThinkTag && !state.hasSentCloseThinkTag) {
                        state.hasSentCloseThinkTag = true
                        "</think>$cleanContent"
                    } else {
                        cleanContent
                    }
                }
                else -> null
            }
        } catch (e: Exception) {
            // 解析失败，说明不是JSON格式
            Timber.e(e, "JSON解析失败: messageId=$messageId")
            null
        }
    }

    /**
     * 🔥 【状态清理】清理指定messageId的解析状态
     */
    private fun clearParseState(messageId: String) {
        parseStates.remove(messageId)
    }

    /* 剥掉 assistant 消息中的 reasoning_content，避免 DeepSeek 400 */
    private fun stripReasoning(msgs: List<ChatMessage>): List<ChatMessage> =
        msgs.map {
            // ChatMessage 只有 role 和 content 字段，无需特殊处理
            it
        }

    private fun isDeepSeek(model: String): Boolean =
        model.contains("deepseek", ignoreCase = true)

    private fun isTopTierModel(model: String): Boolean =
        model.contains("gpt-4", ignoreCase = true) ||
            model.contains("claude", ignoreCase = true) ||
            model.contains("gemini", ignoreCase = true)

    override suspend fun streamChatWithMessageId(request: ChatRequest, messageId: String, offset: Int) {
        val config = networkConfigManager.getCurrentConfig()
        val baseUrl = config.restBase
        val apiKey = config.apiKey

        if (baseUrl.isBlank() || apiKey.isBlank()) {
            Timber.e("❌ 网络配置无效: baseUrl='$baseUrl', apiKey='${apiKey.take(10)}...'")
            notifyConnectionStateChange(ConnectionState.Error(
                RuntimeException("网络配置无效"),
                canRetry = false
            ))
            return
        }

        // 🔥 【状态管理】开始连接
        notifyConnectionStateChange(ConnectionState.Connecting())

        try {
            // 🔥 【重试机制】使用重试策略执行流式传输
            retryStrategy.executeWithRetry {
                performStreamChat(request, messageId, baseUrl, apiKey, offset)
            }.let { result ->
                when (result) {
                    is NetworkResult.Success -> {
                        Timber.d("✅ 流式传输成功")
                    }
                    is NetworkResult.Error -> {
                        Timber.e("❌ 流式传输最终失败: ${result.error}")
                        notifyConnectionStateChange(ConnectionState.Error(
                            result.error.toThrowable(),
                            canRetry = true
                        ))
                    }
                    is NetworkResult.Loading -> {
                        Timber.d("⏳ 流式传输加载中")
                        notifyConnectionStateChange(ConnectionState.Connecting())
                    }
                }
            }

            // 🔥 【726task修复】双路径发送完成信号
            handlerScope.launch {
                // 路径2：TokenBus → Coach模块兼容
                tokenBus.publish(TokenEvent(messageId, "", isComplete = true))
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ 流式传输异常")
            notifyConnectionStateChange(ConnectionState.Error(e, canRetry = true))
            // 🔥 【726task修复】异常时发送完成信号到TokenBus（Coach模块兼容）
            handlerScope.launch {
                tokenBus.publish(TokenEvent(messageId, "", isComplete = true))
            }
        }
    }

    /**
     * 🔥 【重试封装】执行实际的流式传输操作
     */
    private suspend fun performStreamChat(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
        offset: Int
    ): NetworkResult<Unit> {
        return try {
            streamChatInternal(request, messageId, baseUrl, apiKey, offset)
                .catch { e ->
                    Timber.tag("CNET-ERROR").e(e, "流式传输失败")
                    notifyConnectionStateChange(ConnectionState.Error(e, canRetry = true))
                    throw e
                }
                .collect { token ->
                    // 🔥 【心跳更新】收到数据时更新心跳时间
                    updateHeartbeat()

                    if (token.isNotBlank()) {
                        // 🔥 【726task修复】双路径发布：TokenRouter(ThinkingBox) + TokenBus(Coach兼容)
                        handlerScope.launch {
                            // 路径1：TokenRouter → ConversationScope → ThinkingBox
                            tokenRouter.routeToken(messageId, token)
                            // 路径2：TokenBus → Coach模块兼容
                            tokenBus.publish(TokenEvent(messageId, token, isComplete = false))
                        }
                    }
                }

            // 流式传输成功，更新连接状态
            notifyConnectionStateChange(ConnectionState.Connected(
                connectionTime = System.currentTimeMillis(),
                quality = evaluateConnectionQuality()
            ))

            NetworkResult.Success(Unit)
        } catch (e: Exception) {
            NetworkResult.Error(
                com.example.gymbro.shared.models.network.ApiError.Unknown(
                    e.message ?: "Stream chat failed"
                )
            )
        }
    }

    override suspend fun checkConnection(): NetworkResult<Boolean> {
        // 🔥 【连接检查】使用重试策略执行连接检查
        return retryStrategy.executeWithRetry {
            performConnectionCheck()
        }
    }

    /**
     * 🔥 【连接检查】执行实际的连接检查
     */
    private suspend fun performConnectionCheck(): NetworkResult<Boolean> {
        val config = networkConfigManager.getCurrentConfig()
        return try {
            val startTime = System.currentTimeMillis()

            // 使用HTTP HEAD请求检查连接
            val request = Request.Builder()
                .url("${config.restBase}/v1/models")
                .head()
                .header("Authorization", "Bearer ${config.apiKey}")
                .build()

            val response = httpClient.newCall(request).execute()
            val latency = System.currentTimeMillis() - startTime

            response.use {
                if (response.isSuccessful) {
                    // 连接成功，更新连接状态
                    val quality = evaluateConnectionQuality(latency)
                    notifyConnectionStateChange(ConnectionState.Connected(
                        connectionTime = System.currentTimeMillis(),
                        quality = quality
                    ))

                    // 🔥 【减少日志噪音】移除连接检查成功日志
                    NetworkResult.Success(true)
                } else {
                    val error = com.example.gymbro.shared.models.network.ApiError.Http(
                        response.code,
                        response.message,
                    )
                    notifyConnectionStateChange(ConnectionState.Error(
                        RuntimeException("HTTP ${response.code}: ${response.message}"),
                        canRetry = response.code >= 500 // 5xx错误可以重试
                    ))
                    NetworkResult.Error(error)
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ 连接检查异常")
            notifyConnectionStateChange(ConnectionState.Error(e, canRetry = true))
            NetworkResult.Error(
                com.example.gymbro.shared.models.network.ApiError.Unknown(
                    e.message ?: "Connection check failed",
                ),
            )
        }
    }

    /**
     * 🔥 【心跳管理】更新心跳时间
     */
    private fun updateHeartbeat() {
        lastHeartbeatTime = System.currentTimeMillis()
    }

    /**
     * 🔥 【连接质量】基于延迟评估连接质量
     */
    private fun evaluateConnectionQuality(latencyMs: Long = 0L): ConnectionQuality {
        return when {
            latencyMs < 100 -> ConnectionQuality.Excellent
            latencyMs < 300 -> ConnectionQuality.Good
            latencyMs < 800 -> ConnectionQuality.Fair
            latencyMs > 800 -> ConnectionQuality.Poor
            else -> ConnectionQuality.Unknown
        }
    }

    /**
     * 🔥 【心跳检查】检查是否需要心跳检测
     */
    private fun needsHeartbeatCheck(): Boolean {
        val timeSinceLastHeartbeat = System.currentTimeMillis() - lastHeartbeatTime
        return timeSinceLastHeartbeat > heartbeatIntervalMs
    }

    /**
     * 🔥 【状态清理】清理解析状态和连接状态
     */
    private fun cleanup() {
        parseStates.clear()
        protocolMap.clear()
        notifyConnectionStateChange(ConnectionState.Idle)
    }

    override fun getBaseUrl(): String {
        return networkConfigManager.getCurrentConfig().restBase
    }

    override fun pause() {
        // HTTP+SSE doesn't need pause/resume functionality
        // 🔥 【减少日志噪音】移除生命周期日志
        // 🔥 【状态管理】暂停状态监控
        stopStateMonitoring()
    }

    override fun resume() {
        // HTTP+SSE doesn't need pause/resume functionality
        // 🔥 【减少日志噪音】移除生命周期日志
        // 🔥 【状态管理】恢复状态监控
        startStateMonitoring()
    }

    override fun getCurrentState(): WsState {
        // 🔥 【状态真实反映】基于实际连接状态返回WsState，提供具体错误信息
        return when (val state = connectionState.value) {
            is ConnectionState.Connected -> WsState.Open
            is ConnectionState.Connecting, is ConnectionState.Reconnecting -> WsState.Connecting
            is ConnectionState.Disconnected -> WsState.Dead("Disconnected: ${state.reason}")
            is ConnectionState.Error -> WsState.Dead("Error: ${state.throwable.message ?: "Unknown error"}")
            is ConnectionState.Idle -> WsState.Init
        }
    }

    private suspend fun streamChatInternal(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
        offset: Int = 0,
    ): Flow<String> {
        if (isTopTierModel(request.model)) {
            // 🔥 【减少日志噪音】移除协议选择调试日志
            return tryWebSocketWithFallback(request, messageId, baseUrl, apiKey)
        }
        // 🔥 【减少日志噪音】移除协议选择调试日志
        return tryHttpSSEWithFallback(request, messageId, baseUrl, apiKey)
    }

    /* ─────────────────────────────────────────────────────── */
    /* 3. SSE branch                                           */
    /* ─────────────────────────────────────────────────────── */
    private fun streamChatWithSSE(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> = callbackFlow {
        try {
            /* 3-1 build body (DeepSeek strip) */
            val msgs = if (isDeepSeek(request.model)) {
                stripReasoning(request.messages)
            } else {
                request.messages
            }
            val bodyJson = json.encodeToString(
                ChatCompletionRequest(request.model, msgs, true),
            )
            val reqBody = bodyJson.toRequestBody("application/json".toMediaTypeOrNull())

            val httpRequest = Request.Builder()
                .url("$baseUrl/v1/chat/completions")
                .post(reqBody)
                .header("Accept", "text/event-stream")
                .header("Cache-Control", "no-cache")
                .header("Connection", "keep-alive")
                .header("Authorization", "Bearer $apiKey")
                .header("User-Agent", "GymBro/1.0")
                .build()

            // 🔥 【减少日志噪音】移除SSE请求配置调试日志

            /* 3-2 SSE listener */
            val listener = object : EventSourceListener() {
                @Volatile private var finished = false
                private var lifeKeeper: EventSource? = null
                private var lastBeat = SystemClock.elapsedRealtime()

                override fun onOpen(es: EventSource, resp: okhttp3.Response) {
                    // 🔥 【减少日志噪音】移除SSE连接建立日志
                    lifeKeeper = es
                    lastBeat = SystemClock.elapsedRealtime()

                    // 🔥 【状态管理】SSE连接成功
                    notifyConnectionStateChange(ConnectionState.Connected(
                        connectionTime = System.currentTimeMillis(),
                        quality = ConnectionQuality.Good // SSE默认质量为良好
                    ))
                    updateHeartbeat()
                }

                override fun onEvent(es: EventSource, id: String?, type: String?, data: String) {
                    lastBeat = SystemClock.elapsedRealtime()
                    // 🔥 【心跳更新】收到数据时更新心跳
                    updateHeartbeat()

                    if (data == "[DONE]") {
                        finished = true
                        clearParseState(messageId)
                        // 🔥 【减少日志噪音】移除SSE流结束调试日志
                        close()
                        return
                    }

                    // 🔥 【协议检测与上下文注入】
                    var protocol = protocolMap[messageId] ?: DetectedProtocol.UNKNOWN

                    if (protocol == DetectedProtocol.UNKNOWN) {
                        // 首次数据事件，检测协议类型
                        protocol = try {
                            // A simple check: if it can be parsed as JSON with delta content, it's JSON.
                            if (parseJsonContentWithContext(data, messageId) != null) {
                                DetectedProtocol.JSON
                            } else {
                                DetectedProtocol.TEXT
                            }
                        } catch (e: Exception) {
                            DetectedProtocol.TEXT
                        }
                        protocolMap[messageId] = protocol
                        // 🔥 【减少日志噪音】移除协议检测调试日志
                    }

                    val cleanToken: String? = when (protocol) {
                        DetectedProtocol.JSON -> parseJsonContentWithContext(data, messageId)
                        DetectedProtocol.TEXT -> data // Pass through directly
                        DetectedProtocol.UNKNOWN -> null // Wait for more data to determine protocol
                    }

                    if (!cleanToken.isNullOrBlank()) {
                        // 🔥 【726task修复】双路径发布：TokenRouter(ThinkingBox) + TokenBus(Coach兼容)
                        handlerScope.launch {
                            // 路径1：TokenRouter → ConversationScope → ThinkingBox
                            tokenRouter.routeToken(messageId, cleanToken)
                            // 路径2：TokenBus → Coach模块兼容
                            tokenBus.publish(TokenEvent(messageId, cleanToken, isComplete = false))
                        }
                    }
                }

                override fun onClosed(es: EventSource) {
                    // 🔥 【状态清理】清理解析状态
                    clearParseState(messageId)
                    // 🔥 【减少日志噪音】移除SSE连接关闭调试日志

                    // 🔥 【状态管理】连接正常关闭
                    notifyConnectionStateChange(ConnectionState.Disconnected(
                        reason = "Connection closed normally",
                        canRetry = false,
                        lastConnectedTime = System.currentTimeMillis()
                    ))
                    close()
                }

                override fun onFailure(es: EventSource, t: Throwable?, r: okhttp3.Response?) {
                    // 🔥 【状态清理】清理解析状态
                    clearParseState(messageId)
                    Timber.tag("CNET-ERROR").e(t, "SSE连接失败")

                    // 🔥 【状态管理】连接失败
                    val errorMsg = when {
                        r != null -> "HTTP ${r.code}: ${r.message}"
                        t != null -> t.message ?: "Unknown SSE error"
                        else -> "SSE connection failed"
                    }

                    notifyConnectionStateChange(ConnectionState.Error(
                        throwable = t ?: RuntimeException(errorMsg),
                        canRetry = true,
                        retryDelayMs = retryStrategy.calculateRetryDelay(1)
                    ))

                    close(t)
                }
            }

            val es = EventSources.createFactory(httpClient)
                .newEventSource(httpRequest, listener)

            awaitClose {
                clearParseState(messageId)
                // 🔥 【状态管理】连接被取消
                notifyConnectionStateChange(ConnectionState.Disconnected(
                    reason = "Connection cancelled",
                    canRetry = true,
                    lastConnectedTime = System.currentTimeMillis()
                ))
                es.cancel()
            }
        } catch (e: Exception) {
            clearParseState(messageId)
            Timber.tag("CNET-ERROR").e(e, "SSE初始化失败")
            // 🔥 【状态管理】SSE初始化失败
            notifyConnectionStateChange(ConnectionState.Error(
                throwable = e,
                canRetry = true,
                retryDelayMs = retryStrategy.calculateRetryDelay(1)
            ))
            close(e)
        }
    }

    /* ───────── WebSocket with fallback ───────── */
    private suspend fun tryWebSocketWithFallback(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> {
        return try {
            // 简单检查WebSocket连通性
            if (isWebSocketAvailable(baseUrl)) {
                streamChatWithWebSocket(request, messageId, baseUrl, apiKey)
            } else {
                Timber.w("⚠️ WebSocket不可用，降级到HTTP+SSE")
                tryHttpSSEWithFallback(request, messageId, baseUrl, apiKey)
            }
        } catch (e: Exception) {
            Timber.w("⚠️ WebSocket连接失败，降级到HTTP+SSE: ${e.message}")
            tryHttpSSEWithFallback(request, messageId, baseUrl, apiKey)
        }
    }

    private suspend fun isWebSocketAvailable(baseUrl: String): Boolean {
        return try {
            // 使用ProtocolDetector的简化检查
            protocolDetector.isWebSocketAvailable(baseUrl, "dummy-key")
        } catch (e: Exception) {
            Timber.w("WebSocket连通性检查失败: ${e.message}")
            false
        }
    }

    /* ───────── HTTP+SSE with fallback ───────── */
    private suspend fun tryHttpSSEWithFallback(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> {
        return try {
            streamChatWithSSE(request, messageId, baseUrl, apiKey)
        } catch (e: Exception) {
            Timber.e(e, "📡 HTTP+SSE失败，尝试基础HTTP")
            streamChatWithBasicHttp(request, messageId, baseUrl, apiKey)
        }
    }

    /* ───────── WebSocket implementation ───────── */
    private suspend fun streamChatWithWebSocket(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> = flow {
        // WebSocket实现（简化版）
        emit("WebSocket streaming not implemented yet")
    }

    /* ───────── Basic HTTP fallback ───────── */
    private suspend fun streamChatWithBasicHttp(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> = flow {
        // 基础HTTP实现（简化版）
        emit("Basic HTTP fallback not implemented yet")
    }
}

/**
 * ApiError 扩展函数，将 ApiError 转换为 Throwable
 */
private fun com.example.gymbro.shared.models.network.ApiError.toThrowable(): Throwable {
    return when (this) {
        is com.example.gymbro.shared.models.network.ApiError.Http ->
            RuntimeException("HTTP $code: $message")
        is com.example.gymbro.shared.models.network.ApiError.Offline ->
            RuntimeException("Network offline")
        is com.example.gymbro.shared.models.network.ApiError.Parse ->
            RuntimeException("Parse error: $rawResponse")
        is com.example.gymbro.shared.models.network.ApiError.Unknown ->
            RuntimeException("Unknown error: $message")
    }
}
