package com.example.gymbro.features.workout.template.edit.contract

import androidx.compose.runtime.Immutable
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.workout.model.template.TemplateVersion
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.shared.models.workout.TemplateExerciseDto

/**
 * 模板编辑器Contract - P4阶段增强版
 *
 * 🎯 P4阶段新增功能:
 * - 拖拽排序支持
 * - SharedElements转场准备
 * - 自动保存状态管理
 * - 实时预览功能
 * - 完整的MVI 2.0架构支持
 *
 * 🏗️ 架构原则:
 * - Clean Architecture + MVI 2.0模式
 * - 四数据库架构集成
 * - designSystem主题令牌使用
 */
object TemplateEditContract {

    @Immutable
    data class State(
        // === 核心数据 ===
        val template: WorkoutTemplate? = null,
        val originalTemplate: WorkoutTemplate? = null,
        val exercises: List<TemplateExerciseDto> = emptyList(),
        val selectedExercises: List<Exercise> = emptyList(),
        val currentUserId: String = "", // 🔥 修复：用户ID由StateManager动态设置，初始为空
        // === UI状态 ===
        val isLoading: Boolean = false,
        val isSaving: Boolean = false,
        val isDeleting: Boolean = false,
        val hasUnsavedChanges: Boolean = false,
        val showPreview: Boolean = false,
        val isEditMode: Boolean = true,
        // === 拖拽排序状态 (P4新增) ===
        val isDragging: Boolean = false,
        val draggedItemId: String? = null,
        val draggedItemIndex: Int = -1,
        val dropTargetIndex: Int = -1,
        val reorderingEnabled: Boolean = true,
        // === 卡片流式布局状态 (P5新增) ===
        val cardDisplayMode: CardDisplayMode = CardDisplayMode.DETAILED,

        // === 拖拽排序增强 ===
        val draggedExerciseId: String? = null,
        val dragTargetIndex: Int = -1,
        val isDragInProgress: Boolean = false,
        val dragOffset: Float = 0f,
        // === 快速操作状态 ===
        val showQuickActions: Boolean = false,
        val quickActionTargetId: String? = null,
        val swipeOffset: Float = 0f,
        // === 自动保存机制 (P4增强) - 🔥 默认禁用自动保存 ===
        // 🔥 已移除：autoSaveState - 自动保存功能已被移除
        val lastSaveTime: Long? = null,
        // 🔥 已移除：autoSaveEnabled - 自动保存功能已被移除