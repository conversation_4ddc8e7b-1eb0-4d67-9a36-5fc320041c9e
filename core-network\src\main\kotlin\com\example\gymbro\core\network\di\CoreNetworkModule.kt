package com.example.gymbro.core.network.di

import android.content.Context
import com.example.gymbro.core.network.config.NetworkConfig
import com.example.gymbro.core.network.config.NetworkConfigManager
import com.example.gymbro.core.network.config.NetworkConfigProvider
// 🔥 【726task方案修复】添加TokenBus和TokenRouter相关导入
import com.example.gymbro.core.network.eventbus.TokenBus
import com.example.gymbro.core.network.router.TokenRouter
import com.example.gymbro.core.network.security.StringXmlEscaper
import com.example.gymbro.core.di.qualifiers.ApplicationScope
import com.example.gymbro.core.network.monitor.AndroidNetworkMonitor
import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.network.monitor.NetworkWatchdog
import com.example.gymbro.core.network.monitor.NetworkWatchdogImpl
import com.example.gymbro.core.network.protocol.AdaptiveStreamClient
import com.example.gymbro.core.network.protocol.DefaultStreamClientStateProvider
import com.example.gymbro.core.network.protocol.ProtocolDetector
import com.example.gymbro.core.network.protocol.StreamClientStateProvider
import com.example.gymbro.core.network.rest.RestClient
import com.example.gymbro.core.network.rest.RestClientImpl
import com.example.gymbro.core.network.retry.ExponentialBackoffRetryStrategy
import com.example.gymbro.core.network.retry.NetworkRetryStrategy
import com.example.gymbro.core.network.state.NetworkStateMonitor
import com.example.gymbro.core.network.state.NetworkStateMonitorImpl
import com.example.gymbro.core.network.ws.LlmStreamClient

import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Named
import javax.inject.Singleton

/**
 * Core Network模块DI配置 - 专注于HTTP+SSE实现 + 726task Token流系统
 *
 * 🎯 核心功能：
 * 1. 配置OkHttp客户端支持SSE
 * 2. 提供AdaptiveStreamClient作为主要流式客户端
 * 3. 🔥 【726task修复】提供TokenBus+TokenRouter完整token流系统
 * 4. 移除复杂的WebSocket依赖
 * 5. 简化协议检测配置
 */
@Module
@InstallIn(SingletonComponent::class)
object CoreNetworkModule {

    // ==================== 726task Token流系统 ====================

    /**
     * 🔥 【726task方案修复】提供TokenBus - 全局token事件总线
     */
    @Provides
    @Singleton
    fun provideTokenBus(
        @ApplicationScope applicationScope: CoroutineScope,
    ): TokenBus {
        Timber.d("🔧 [726task] 创建TokenBus")
        return TokenBus(applicationScope)
    }

    /**
     * 🔥 【726task方案修复】提供TokenRouter - ConversationScope路由管理
     */
    @Provides
    @Singleton
    fun provideTokenRouter(
        @ApplicationScope applicationScope: CoroutineScope,
    ): TokenRouter {
        Timber.d("🔧 [726task] 创建TokenRouter")
        return TokenRouter(applicationScope)
    }

    /**
     * 🔥 【726task方案修复】提供StringXmlEscaper - XML安全处理
     */
    @Provides
    @Singleton
    fun provideStringXmlEscaper(): StringXmlEscaper {
        Timber.d("🔧 [726task] 创建StringXmlEscaper")
        return StringXmlEscaper()
    }

    // ==================== 原有网络配置 ====================

    /**
     * 🔥 简化配置管理器 - 减少重复同步
     */
    @Provides
    @Singleton
    fun provideNetworkConfigManager(
        configProvider: NetworkConfigProvider,
    ): NetworkConfigManager {
        Timber.d("🔧 创建简化NetworkConfigManager")
        val manager = NetworkConfigManager()

        // 使用Provider的初始配置初始化Manager
        val initialConfig = configProvider.getConfig()
        manager.switchProvider(initialConfig, "系统初始化")

        // 🔥 简化动态配置同步 - 减少频繁触发
        CoroutineScope(Dispatchers.IO).launch {
            configProvider.observeConfig()
                .distinctUntilChanged() // 🎯 只有配置真正变化时才触发
                .collect { newConfig ->
                    Timber.d("🔄 配置变更同步")
                    manager.switchProvider(newConfig, "配置变更")
                }
        }

        Timber.d("🔧 NetworkConfigManager配置完成")
        return manager
    }

    /**
     * 🔥 提供当前NetworkConfig - 兼容需要直接注入NetworkConfig的地方
     */
    @Provides
    @Singleton
    fun provideCurrentNetworkConfig(
        configManager: NetworkConfigManager,
    ): NetworkConfig {
        return configManager.getCurrentConfig()
    }

    /**
     * 提供专用于SSE的OkHttp客户端
     */
    @Provides
    @Singleton
    @Named("sse_client")
    fun provideSSEOkHttpClient(): OkHttpClient {
        Timber.d("🔧 配置SSE专用OkHttpClient - 无证书钉扎，使用系统证书验证")

        return OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS) // 🔥 SSE需要较长的读取超时
            .writeTimeout(30, TimeUnit.SECONDS)
            .callTimeout(120, TimeUnit.SECONDS) // 🔥 SSE流式传输需要更长的调用超时
            .retryOnConnectionFailure(true)
            // 🔥 【HTTP协议限制】强制使用HTTP/1.1，避免HTTP/3协议问题
            .protocols(listOf(okhttp3.Protocol.HTTP_1_1))
            .addInterceptor(
                HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.HEADERS // 🔥 只记录头部，避免SSE数据污染日志
                },
            )
            // 🔥 【SSL调试】添加网络事件监听器以诊断连接问题
            .eventListener(
                object : okhttp3.EventListener() {
                    override fun connectStart(
                        call: okhttp3.Call,
                        inetSocketAddress: java.net.InetSocketAddress,
                        proxy: java.net.Proxy,
                    ) {
                        Timber.d("🔌 开始连接: ${inetSocketAddress.hostName}:${inetSocketAddress.port}")
                    }

                    override fun secureConnectStart(call: okhttp3.Call) {
                        Timber.d("🔒 开始SSL握手")
                    }

                    override fun secureConnectEnd(
                        call: okhttp3.Call,
                        handshake: okhttp3.Handshake?,
                    ) {
                        Timber.d("🔒 SSL握手完成: ${handshake?.tlsVersion} ${handshake?.cipherSuite}")
                    }

                    override fun connectFailed(
                        call: okhttp3.Call,
                        inetSocketAddress: java.net.InetSocketAddress,
                        proxy: java.net.Proxy,
                        protocol: okhttp3.Protocol?,
                        ioe: java.io.IOException,
                    ) {
                        Timber.e(ioe, "❌ 连接失败: ${inetSocketAddress.hostName}:${inetSocketAddress.port}")
                    }

                    override fun callStart(call: okhttp3.Call) {
                        Timber.d("📞 开始HTTP调用: ${call.request().url}")
                    }

                    override fun responseHeadersEnd(
                        call: okhttp3.Call,
                        response: okhttp3.Response,
                    ) {
                        Timber.d("📡 响应头接收完成: ${response.code} ${response.message}")
                        Timber.d("🔗 使用协议: ${response.protocol}")
                        // 🔥 特别关注alt-svc头
                        response.header("alt-svc")?.let { altSvc ->
                            Timber.w("⚠️ 服务器支持协议升级: $altSvc")
                        }
                    }
                },
            ).build()
    }

    /**
     * 提供REST专用OkHttpClient - 保持现有REST功能
     */
    @Provides
    @Singleton
    @Named("rest_client")
    fun provideRestOkHttpClient(configManager: NetworkConfigManager): OkHttpClient {
        Timber.d("🔧 配置REST专用OkHttpClient (动态配置)")

        // 获取当前配置
        val config = configManager.getCurrentConfig()

        return OkHttpClient.Builder()
            .connectTimeout(config.connectTimeoutSec.toLong(), TimeUnit.SECONDS)
            .readTimeout(config.readTimeoutSec.toLong(), TimeUnit.SECONDS)
            .writeTimeout(config.writeTimeoutSec.toLong(), TimeUnit.SECONDS)
            .retryOnConnectionFailure(config.enableRetry)
            .build()
    }

    /**
     * 提供JSON序列化器
     */
    @Provides
    @Singleton
    @Named("core_network_json")
    fun provideCoreNetworkJson(): Json {
        return Json {
            ignoreUnknownKeys = true
            isLenient = true
            encodeDefaults = true
            prettyPrint = false
            coerceInputValues = true
        }
    }

    /**
     * 提供SSE专用JSON序列化器
     */
    @Provides
    @Singleton
    @Named("sse_json")
    fun provideSSEJson(): Json {
        return Json {
            ignoreUnknownKeys = true
            isLenient = true
            encodeDefaults = true
            prettyPrint = false // 🔥 网络传输不需要格式化
        }
    }

    /**
     * 提供网络监控器
     */
    @Provides
    @Singleton
    fun provideNetworkMonitor(
        @ApplicationContext context: Context,
    ): NetworkMonitor {
        return AndroidNetworkMonitor(context)
    }

    /**
     * 提供网络监控狗
     */
    @Provides
    @Singleton
    fun provideNetworkWatchdog(
        networkMonitor: NetworkMonitor,
    ): NetworkWatchdog {
        return NetworkWatchdogImpl(
            networkMonitor = networkMonitor,
            debounceTimeMs = 2000L,
        )
    }

    /**
     * 提供协议检测器
     */
    @Provides
    @Singleton
    fun provideProtocolDetector(
        @Named("sse_client") httpClient: OkHttpClient,
    ): ProtocolDetector {
        return ProtocolDetector(httpClient)
    }

    /**
     * 提供自适应流式客户端 - 主要的LlmStreamClient实现
     * 🔥 【726task修复】集成TokenRouter，实现正确的token路由架构
     */
    @Provides
    @Singleton
    fun provideAdaptiveStreamClient(
        protocolDetector: ProtocolDetector,
        @Named("sse_client") httpClient: OkHttpClient,
        @Named("sse_json") json: Json,
        configManager: NetworkConfigManager,
        tokenBus: com.example.gymbro.core.network.eventbus.TokenBus,
        tokenRouter: com.example.gymbro.core.network.router.TokenRouter, // 🔥 【726task修复】添加TokenRouter依赖
        stringXmlEscaper: com.example.gymbro.core.network.security.StringXmlEscaper,
    ): AdaptiveStreamClient {
        return AdaptiveStreamClient(
            httpClient = httpClient,
            json = json,
            tokenBus = tokenBus,
            tokenRouter = tokenRouter, // 🔥 【726task修复】注入TokenRouter
            networkConfigManager = configManager,
            protocolDetector = protocolDetector,
            stringXmlEscaper = stringXmlEscaper,
        )
    }

    /**
     * 提供LlmStreamClient接口实现
     * 🔥 直接使用AdaptiveStreamClient，无需额外绑定
     */
    @Provides
    @Singleton
    fun provideLlmStreamClient(
        adaptiveStreamClient: AdaptiveStreamClient,
    ): LlmStreamClient {
        return adaptiveStreamClient
    }

    /**
     * 🔥 提供RestClient实例 - 使用NetworkConfigManager
     */
    @Provides
    @Singleton
    fun provideRestClientImpl(
        @Named("rest_client") okHttpClient: OkHttpClient,
        @Named("core_network_json") json: Json,
        configManager: NetworkConfigManager,
        networkMonitor: NetworkMonitor,
    ): RestClientImpl {
        Timber.d("🔧 创建RestClient (动态配置版本)")

        return RestClientImpl(
            baseOkHttpClient = okHttpClient,
            configManager = configManager,
            networkMonitor = networkMonitor,
        )
    }

    /**
     * 🔥 提供网络状态监控器
     */
    @Provides
    @Singleton
    fun provideNetworkStateMonitor(
        @ApplicationContext context: Context
    ): NetworkStateMonitor {
        return NetworkStateMonitorImpl(context)
    }

    /**
     * 🔥 提供网络重试策略
     */
    @Provides
    @Singleton
    fun provideNetworkRetryStrategy(): NetworkRetryStrategy {
        return ExponentialBackoffRetryStrategy(
            initialDelayMs = 1000L,
            maxDelayMs = 30000L,
            maxRetries = 5
        )
    }

    /**
     * 🔥 提供流客户端状态提供者
     */
    @Provides
    @Singleton
    fun provideStreamClientStateProvider(): StreamClientStateProvider {
        return DefaultStreamClientStateProvider()
    }


}

/**
 * Core Network绑定模块
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class CoreNetworkBindingModule {

    @Binds
    @Singleton
    abstract fun bindRestClient(impl: RestClientImpl): RestClient
}
