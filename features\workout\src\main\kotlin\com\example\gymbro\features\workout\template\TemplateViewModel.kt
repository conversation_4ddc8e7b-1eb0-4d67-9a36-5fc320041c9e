package com.example.gymbro.features.workout.template

import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.features.workout.template.internal.effect.TemplateEffectHandler
import com.example.gymbro.features.workout.template.internal.reducer.TemplateReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Template模块ViewModel - MVI 2.0架构实现
 *
 * 🎯 核心特性：
 * 1. 继承BaseMviViewModel，遵循MVI 2.0标准
 * 2. 使用TemplateReducer处理状态变更逻辑
 * 3. 使用TemplateEffectHandler处理副作用
 * 4. 支持结构化并发和生命周期管理
 * 5. 提供便捷API用于UI层调用
 *
 * 🏗️ 架构标准：
 * - 继承BaseMviViewModel
 * - 使用TemplateReducer
 * - 完整的Effect处理
 * - 参考Profile模块的成功实现模式
 */
@HiltViewModel
class TemplateViewModel @Inject constructor(
    private val templateReducer: TemplateReducer,
    private val templateEffectHandler: TemplateEffectHandler,
    private val logger: Logger,
) : BaseMviViewModel<TemplateContract.Intent, TemplateContract.State, TemplateContract.Effect>(
    initialState = TemplateContract.State(),
) {

    // 🔥 修复：提供Reducer实例给BaseMviViewModel - 兼容性适配器
    override val reducer: Reducer<TemplateContract.Intent, TemplateContract.State, TemplateContract.Effect> =
        object : Reducer<TemplateContract.Intent, TemplateContract.State, TemplateContract.Effect> {
            override fun reduce(
                intent: TemplateContract.Intent,
                currentState: TemplateContract.State,
            ): ReduceResult<TemplateContract.State, TemplateContract.Effect> {
                val customResult = templateReducer.reduce(intent, currentState)

                // 🔥 关键修复：将自定义ReduceResult转换为标准ReduceResult
                return if (customResult.effect != null) {
                    // 单个effect转换为effects列表
                    ReduceResult.withEffect(customResult.newState, customResult.effect)
                } else {
                    // 无effect的情况
                    ReduceResult.stateOnly(customResult.newState)
                }
            }
        }

    // === 初始化 ===

    init {
        logger.d("TemplateViewModel", "TemplateViewModel初始化，开始加载模板数据")
        println("🔧 [DEBUG] TemplateViewModel初始化，开始加载模板数据")

        // 🔥 MVI 2.0: 初始化EffectHandler
        initializeEffectHandler()

        // 初始化数据加载
        dispatch(TemplateContract.Intent.LoadTemplates)
        dispatch(TemplateContract.Intent.LoadDrafts)
    }

    // === MVI 2.0: EffectHandler初始化 ===

    override fun initializeEffectHandler() {
        // 监听Effect并处理副作用
        viewModelScope.launch {
            effect.collect { effect ->
                logger.d("TemplateViewModel", "处理Effect: ${effect::class.simpleName}")
                println("🔧 [DEBUG] TemplateViewModel: 处理Effect: ${effect::class.simpleName}")

                // 🔥 修复：通过EffectHandler处理Effect而不是Intent
                templateEffectHandler.handleEffect(
                    effect = effect,
                    currentState = currentState,
                    effectScope = viewModelScope,
                    dispatch = { resultIntent ->
                        logger.d(
                            "TemplateViewModel",
                            "EffectHandler返回Intent: ${resultIntent::class.simpleName}",
                        )
                        println(
                            "🔧 [DEBUG] TemplateViewModel: EffectHandler返回Intent: ${resultIntent::class.simpleName}",
                        )
                        dispatch(resultIntent)
                    },
                    emitEffect = { resultEffect ->
                        // 处理嵌套Effect
                        sendEffect(resultEffect)
                    },
                )
            }
        }
    }

    // === 调试和监控 ===

    internal fun getStateSummary(): String {
        val state = currentState
        return buildString {
            append("TemplateState(")
            append("loading=${state.isLoading}, ")
            append("templatesCount=${state.templates.size}, ")
            append("hasError=${state.error != null}")
            append(")")
        }
    }

    // === 便捷API - 为UI层提供友好的方法调用 ===

    /**
     * 加载模板列表
     */
    fun loadTemplates() {
        logger.d("TemplateViewModel", "加载模板列表")
        dispatch(TemplateContract.Intent.LoadTemplates)
    }

    /**
     * 刷新模板列表
     */
    fun refreshTemplates() {
        dispatch(TemplateContract.Intent.RefreshTemplates)
    }

    /**
     * 搜索模板
     */
    fun searchTemplates(query: String) {
        logger.d("TemplateViewModel", "搜索模板: $query")
        dispatch(TemplateContract.Intent.SearchTemplates(query))
    }

    /**
     * 按分类筛选
     */
    fun filterByCategory(category: String) {
        logger.d("TemplateViewModel", "按分类筛选: $category")
        dispatch(TemplateContract.Intent.FilterByCategory(category))
    }

    /**
     * 选择模板
     */
    fun selectTemplate(templateId: String) {
        logger.d("TemplateViewModel", "选择模板: $templateId")
        dispatch(TemplateContract.Intent.SelectTemplate(templateId))
    }

    /**
     * 删除模板
     */
    fun deleteTemplate(templateId: String) {
        logger.d("TemplateViewModel", "删除模板: $templateId")
        dispatch(TemplateContract.Intent.DeleteTemplate(templateId))
    }

    /**
     * 复制模板
     */
    fun duplicateTemplate(templateId: String) {
        logger.d("TemplateViewModel", "复制模板: $templateId")
        dispatch(TemplateContract.Intent.DuplicateTemplate(templateId))
    }

    /**
     * 切换模板收藏状态
     */
    fun toggleTemplateFavorite(templateId: String) {
        logger.d("TemplateViewModel", "切换模板收藏: $templateId")
        dispatch(TemplateContract.Intent.ToggleTemplateFavorite(templateId))
    }

    /**
     * 从模板开始训练
     */
    fun startWorkoutFromTemplate(templateId: String) {
        logger.d("TemplateViewModel", "从模板开始训练: $templateId")
        dispatch(TemplateContract.Intent.StartWorkoutFromTemplate(templateId))
    }

    /**
     * 清除错误
     */
    fun clearError() {
        logger.d("TemplateViewModel", "清除错误")
        dispatch(TemplateContract.Intent.ClearError)
    }

    /**
     * 重置导航状态 - 修复按钮禁用问题
     */
    fun resetNavigationState() {
        logger.d("TemplateViewModel", "重置导航状态")
        dispatch(TemplateContract.Intent.ResetNavigationState)
    }

    // === P4新增：搜索功能API ===

    /**
     * 切换搜索栏显示状态
     */
    fun toggleSearch() {
        logger.d("TemplateViewModel", "切换搜索栏")
        dispatch(TemplateContract.Intent.ToggleSearch)
    }

    /**
     * 显示搜索栏
     */
    fun showSearch() {
        logger.d("TemplateViewModel", "显示搜索栏")
        dispatch(TemplateContract.Intent.ShowSearch)
    }

    /**
     * 隐藏搜索栏
     */
    fun hideSearch() {
        logger.d("TemplateViewModel", "隐藏搜索栏")
        dispatch(TemplateContract.Intent.HideSearch)
    }

    /**
     * 清除筛选条件
     */
    fun clearFilters() {
        logger.d("TemplateViewModel", "清除筛选条件")
        dispatch(TemplateContract.Intent.ClearFilters)
    }

    // === P4新增：自动保存功能API ===

    /**
     * 启用自动保存
     */
    // 🔥 已移除：enableAutoSave - 自动保存功能已被移除

    /**
     * 禁用自动保存
     */
    // 🔥 已移除：disableAutoSave - 自动保存功能已被移除

    /**
     * 保存到缓存
     */
    fun saveToCache() {
        logger.d("TemplateViewModel", "保存到缓存")
        dispatch(TemplateContract.Intent.SaveToCache)
    }

    /**
     * 从缓存恢复
     */
    fun restoreFromCache() {
        logger.d("TemplateViewModel", "从缓存恢复")
        dispatch(TemplateContract.Intent.RestoreFromCache)
    }

    /**
     * 清除指定模板的缓存
     */
    fun clearCache(templateId: String) {
        logger.d("TemplateViewModel", "清除缓存: $templateId")
        dispatch(TemplateContract.Intent.ClearCache(templateId))
    }

    /**
     * 显示缓存恢复对话框
     */
    fun showCacheRestoreDialog(
        cachedTemplates: List<com.example.gymbro.shared.models.workout.WorkoutTemplateDto>,
    ) {
        logger.d("TemplateViewModel", "显示缓存恢复对话框")
        dispatch(TemplateContract.Intent.ShowCacheRestoreDialog(cachedTemplates))
    }

    /**
     * 隐藏缓存恢复对话框
     */
    fun hideCacheRestoreDialog() {
        logger.d("TemplateViewModel", "隐藏缓存恢复对话框")
        dispatch(TemplateContract.Intent.HideCacheRestoreDialog)
    }

    /**
     * 恢复特定缓存
     */
    fun restoreSpecificCache(templateId: String) {
        logger.d("TemplateViewModel", "恢复特定缓存: $templateId")
        dispatch(TemplateContract.Intent.RestoreSpecificCache(templateId))
    }

    /**
     * 更新自动保存状态
     */
    // 🔥 已移除：updateAutoSaveState - 自动保存功能已被移除

    /**
     * 显示删除对话框
     */
    fun showDeleteDialog() {
        logger.d("TemplateViewModel", "显示删除对话框")
        dispatch(TemplateContract.Intent.ShowDeleteDialog)
    }

    /**
     * 隐藏删除对话框
     */
    fun hideDeleteDialog() {
        logger.d("TemplateViewModel", "隐藏删除对话框")
        dispatch(TemplateContract.Intent.HideDeleteDialog)
    }

    /**
     * 导航到新建模板
     */
    fun navigateToNewTemplate() {
        logger.d("TemplateViewModel", "导航到新建模板")
        sendEffect(TemplateContract.Effect.NavigateToNewTemplate)
    }

    /**
     * 导航到编辑模板
     */
    fun navigateToEditTemplate(templateId: String) {
        logger.d("TemplateViewModel", "导航到编辑模板: $templateId")
        sendEffect(TemplateContract.Effect.NavigateToEditTemplate(templateId))
    }

    // === 模板编辑API - 🔧 新增 ===

    /**
     * 加载指定模板进行编辑
     */
    fun loadTemplate(templateId: String) {
        logger.d("TemplateViewModel", "加载模板进行编辑: $templateId")
        dispatch(TemplateContract.Intent.LoadTemplate(templateId))
    }

    /**
     * 创建新模板
     */
    fun createNewTemplate() {
        logger.d("TemplateViewModel", "创建新模板")
        // TODO: 添加CreateNewTemplate Intent到TemplateContract
        // dispatch(TemplateContract.Intent.CreateNewTemplate)
    }

    // 🔥 修复：移除主界面的保存逻辑，避免与 TemplateEditScreen 冲突
    // 模板的更新和保存应该只在 TemplateEditScreen 中进行
    // 主界面只负责显示、导航和排序功能

    /**
     * 注意：模板的编辑和保存功能已移至 TemplateEditScreen
     * 主界面只负责：
     * 1. 模板列表显示
     * 2. 导航到编辑界面
     * 3. 模板排序（如果需要）
     * 4. 删除操作
     */

    // 🔥 修复：移除主界面的动作编辑逻辑
    // 所有动作的添加、移除、更新都应该在 TemplateEditScreen 中进行
    // 主界面不应该直接操作模板内容

    /**
     * 重新排序动作
     */
    fun reorderExercises(fromIndex: Int, toIndex: Int) {
        logger.d("TemplateViewModel", "重新排序动作: $fromIndex -> $toIndex")
        dispatch(TemplateContract.Intent.ReorderExercises(fromIndex, toIndex))
    }

    /**
     * 导航到动作选择器
     */
    fun navigateToExerciseSelector() {
        logger.d("TemplateViewModel", "导航到动作选择器")
        sendEffect(TemplateContract.Effect.NavigateToExerciseSelector(multipleSelection = true))
    }

    /**
     * 返回上一页
     */
    fun navigateBack() {
        logger.d("TemplateViewModel", "返回上一页")
        sendEffect(TemplateContract.Effect.NavigateBack)
    }

    /**
     * 开始拖拽模板
     */
    fun startDragTemplate(templateId: String, currentIndex: Int) {
        logger.d("TemplateViewModel", "开始拖拽模板: $templateId at index $currentIndex")
        dispatch(TemplateContract.Intent.StartDragTemplate(templateId, currentIndex))
    }

    /**
     * 结束拖拽模板
     */
    fun endDragTemplate(templateId: String, finalIndex: Int) {
        logger.d("TemplateViewModel", "结束拖拽模板: $templateId to index $finalIndex")
        dispatch(TemplateContract.Intent.EndDragTemplate(templateId, finalIndex))
    }

    /**
     * 开始拖拽草稿
     */
    fun startDragDraft(draftId: String, currentIndex: Int) {
        logger.d("TemplateViewModel", "开始拖拽草稿: $draftId at index $currentIndex")
        dispatch(TemplateContract.Intent.StartDragDraft(draftId, currentIndex))
    }

    /**
     * 结束拖拽草稿
     */
    fun endDragDraft(draftId: String, finalIndex: Int) {
        logger.d("TemplateViewModel", "结束拖拽草稿: $draftId to index $finalIndex")
        dispatch(TemplateContract.Intent.EndDragDraft(draftId, finalIndex))
    }

    /**
     * 模板置顶
     */
    fun moveTemplateToTop(templateId: String) {
        logger.d("TemplateViewModel", "模板置顶: $templateId")
        dispatch(TemplateContract.Intent.MoveTemplateToTop(templateId))
    }

    /**
     * 草稿置顶
     */
    fun moveDraftToTop(draftId: String) {
        logger.d("TemplateViewModel", "草稿置顶: $draftId")
        dispatch(TemplateContract.Intent.MoveDraftToTop(draftId))
    }

    // === 草稿管理API - ✨ 新增 ===

    /**
     * 加载草稿列表
     */
    fun loadDrafts() {
        logger.d("TemplateViewModel", "加载草稿列表")
        dispatch(TemplateContract.Intent.LoadDrafts)
    }

    /**
     * 刷新草稿列表
     */
    fun refreshDrafts() {
        logger.d("TemplateViewModel", "🔄 刷新草稿列表被调用")
        logger.i("TemplateViewModel", "🔄 [INFO] 刷新草稿列表被调用")
        dispatch(TemplateContract.Intent.RefreshDrafts)
        logger.d("TemplateViewModel", "🔄 RefreshDrafts Intent已发送")
        logger.i("TemplateViewModel", "🔄 [INFO] RefreshDrafts Intent已发送")
    }

    /**
     * 创建新草稿
     */
    fun createNewDraft(name: String = "新训练模板") {
        logger.d("TemplateViewModel", "创建新草稿: $name")
        dispatch(TemplateContract.Intent.CreateNewDraft(name))
    }

    /**
     * 编辑草稿
     */
    fun editDraft(draftId: String) {
        logger.d("TemplateViewModel", "编辑草稿: $draftId")
        sendEffect(TemplateContract.Effect.NavigateToDraftEditor(draftId))
    }

    /**
     * 删除草稿
     */
    fun deleteDraft(draftId: String) {
        logger.d("TemplateViewModel", "删除草稿: $draftId")
        dispatch(TemplateContract.Intent.DeleteDraft(draftId))
    }

    /**
     * 转正草稿
     */
    fun promoteDraft(draftId: String) {
        logger.d("TemplateViewModel", "转正草稿: $draftId")
        dispatch(TemplateContract.Intent.PromoteDraft(draftId))
    }

    /**
     * 切换Tab
     */
    fun switchTab(tab: TemplateContract.TemplateTab) {
        logger.d("TemplateViewModel", "切换Tab: ${tab.displayName}")
        dispatch(TemplateContract.Intent.SwitchTab(tab))
    }

    /**
     * 导航到创建草稿
     */
    fun navigateToCreateDraft() {
        logger.d("TemplateViewModel", "导航到创建草稿")
        sendEffect(TemplateContract.Effect.NavigateToCreateDraft)
    }

    /**
     * 导航到草稿编辑器
     */
    fun navigateToDraftEditor(draftId: String) {
        logger.d("TemplateViewModel", "导航到草稿编辑器: $draftId")
        sendEffect(TemplateContract.Effect.NavigateToDraftEditor(draftId))
    }
}
