package com.example.gymbro.features.thinkingbox.debug

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【726task修复验证】Token流路径验证工具
 * 
 * 验证修复后的架构是否正确实现：
 * AdaptiveStreamClient → TokenRouter → ConversationScope → ThinkingBoxViewModel
 */
@Singleton
class TokenFlowValidation @Inject constructor(
    private val tokenRouter: com.example.gymbro.core.network.router.TokenRouter,
    private val adaptiveStreamClient: com.example.gymbro.core.network.protocol.AdaptiveStreamClient,
) {
    
    companion object {
        private const val TAG = "TokenFlowValidation"
        private const val TEST_MESSAGE_ID = "test-token-flow-726task"
    }
    
    /**
     * 验证Token流路径是否正确工作
     */
    suspend fun validateTokenFlow(): ValidationResult {
        return try {
            Timber.tag(TAG).i("🔍 开始验证Token流路径...")
            
            // 1. 验证TokenRouter是否正常工作
            val routerResult = validateTokenRouter()
            if (!routerResult.success) {
                return ValidationResult(false, "TokenRouter验证失败: ${routerResult.message}")
            }
            
            // 2. 验证ConversationScope是否正确接收token
            val scopeResult = validateConversationScope()
            if (!scopeResult.success) {
                return ValidationResult(false, "ConversationScope验证失败: ${scopeResult.message}")
            }
            
            // 3. 验证完整的数据流路径
            val flowResult = validateCompleteFlow()
            if (!flowResult.success) {
                return ValidationResult(false, "完整数据流验证失败: ${flowResult.message}")
            }
            
            Timber.tag(TAG).i("✅ Token流路径验证成功！")
            ValidationResult(true, "所有验证通过")
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ Token流路径验证异常")
            ValidationResult(false, "验证异常: ${e.message}")
        }
    }
    
    /**
     * 验证TokenRouter基本功能
     */
    private suspend fun validateTokenRouter(): ValidationResult {
        return try {
            Timber.tag(TAG).d("🔍 验证TokenRouter...")
            
            // 创建测试scope
            val scope = tokenRouter.getOrCreateScope(TEST_MESSAGE_ID)
            
            // 验证scope是否创建成功
            if (!scope.isActive()) {
                return ValidationResult(false, "ConversationScope创建失败")
            }
            
            // 测试token路由
            val testToken = "test-token-${System.currentTimeMillis()}"
            tokenRouter.routeToken(TEST_MESSAGE_ID, testToken)
            
            // 验证token是否到达scope
            var tokenReceived = false
            val job = CoroutineScope(Dispatchers.IO).launch {
                scope.tokens.take(1).collect { token ->
                    if (token == testToken) {
                        tokenReceived = true
                    }
                }
            }
            
            delay(1000) // 等待1秒
            job.cancel()
            
            if (!tokenReceived) {
                return ValidationResult(false, "Token未正确路由到ConversationScope")
            }
            
            Timber.tag(TAG).d("✅ TokenRouter验证通过")
            ValidationResult(true, "TokenRouter工作正常")
            
        } catch (e: Exception) {
            ValidationResult(false, "TokenRouter验证异常: ${e.message}")
        }
    }
    
    /**
     * 验证ConversationScope功能
     */
    private suspend fun validateConversationScope(): ValidationResult {
        return try {
            Timber.tag(TAG).d("🔍 验证ConversationScope...")
            
            val scope = tokenRouter.getOrCreateScope(TEST_MESSAGE_ID)
            
            // 验证token流是否正常
            val testTokens = listOf("token1", "token2", "token3")
            val receivedTokens = mutableListOf<String>()
            
            val job = CoroutineScope(Dispatchers.IO).launch {
                scope.tokens.take(testTokens.size).collect { token ->
                    receivedTokens.add(token)
                }
            }
            
            // 发送测试tokens
            testTokens.forEach { token ->
                scope.emitToken(token)
                delay(100)
            }
            
            delay(1000) // 等待收集完成
            job.cancel()
            
            if (receivedTokens.size != testTokens.size) {
                return ValidationResult(false, "Token数量不匹配: 期望${testTokens.size}, 实际${receivedTokens.size}")
            }
            
            if (receivedTokens != testTokens) {
                return ValidationResult(false, "Token内容不匹配")
            }
            
            Timber.tag(TAG).d("✅ ConversationScope验证通过")
            ValidationResult(true, "ConversationScope工作正常")
            
        } catch (e: Exception) {
            ValidationResult(false, "ConversationScope验证异常: ${e.message}")
        }
    }
    
    /**
     * 验证完整的数据流路径
     */
    private suspend fun validateCompleteFlow(): ValidationResult {
        return try {
            Timber.tag(TAG).d("🔍 验证完整数据流...")
            
            // 验证AdaptiveStreamClient的连接状态
            val connectionState = adaptiveStreamClient.getCurrentState()
            Timber.tag(TAG).d("AdaptiveStreamClient状态: $connectionState")
            
            // 验证TokenRouter的调试信息
            val debugInfo = tokenRouter.getDebugInfo()
            Timber.tag(TAG).d("TokenRouter调试信息:\n$debugInfo")
            
            // 清理测试数据
            tokenRouter.releaseScope(TEST_MESSAGE_ID)
            
            Timber.tag(TAG).d("✅ 完整数据流验证通过")
            ValidationResult(true, "完整数据流工作正常")
            
        } catch (e: Exception) {
            ValidationResult(false, "完整数据流验证异常: ${e.message}")
        }
    }
    
    /**
     * 验证结果数据类
     */
    data class ValidationResult(
        val success: Boolean,
        val message: String
    )
}
