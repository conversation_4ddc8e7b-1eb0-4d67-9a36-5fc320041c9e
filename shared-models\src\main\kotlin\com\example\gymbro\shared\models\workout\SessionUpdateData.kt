package com.example.gymbro.shared.models.workout

import kotlinx.serialization.Serializable

/**
 * 会话更新数据类
 *
 * 用于更新训练会话的部分信息，支持可选字段更新
 * 遵循shared-models模块的纯DTO设计原则，不包含业务逻辑
 *
 * @property sessionName 会话名称（可选更新）
 * @property notes 会话备注（可选更新）
 * @property startTime 开始时间（可选更新）
 * @property endTime 结束时间（可选更新）
 * @property totalDuration 总时长秒数（可选更新）
 * @property isCompleted 是否完成（可选更新）
 * @property exerciseUpdates 动作执行数据更新列表（可选更新）
 */
@Serializable
data class SessionUpdateData(
    val sessionName: String? = null,
    val notes: String? = null,
    val startTime: Long? = null,
    val endTime: Long? = null,
    val totalDuration: Int? = null,
    val isCompleted: Boolean? = null,
    val exerciseUpdates: List<ExerciseUpdateData>? = null,
) {
    companion object {
        /**
         * 创建空的更新数据
         */
        fun empty(): SessionUpdateData = SessionUpdateData()

        /**
         * 仅更新名称
         */
        fun nameOnly(sessionName: String): SessionUpdateData = SessionUpdateData(sessionName = sessionName)

        /**
         * 仅更新备注
         */
        fun notesOnly(notes: String): SessionUpdateData = SessionUpdateData(notes = notes)

        /**
         * 仅更新完成状态
         */
        fun completionOnly(isCompleted: Boolean): SessionUpdateData = SessionUpdateData(isCompleted = isCompleted)

        /**
         * 更新时间范围
         */
        fun timeRange(startTime: Long, endTime: Long): SessionUpdateData = 
            SessionUpdateData(startTime = startTime, endTime = endTime)
    }

    /**
     * 检查是否有任何字段需要更新
     */
    fun hasUpdates(): Boolean {
        return sessionName != null ||
            notes != null ||
            startTime != null ||
            endTime != null ||
            totalDuration != null ||
            isCompleted != null ||
            exerciseUpdates != null
    }

    /**
     * 获取更新字段的数量
     */
    fun getUpdateCount(): Int {
        var count = 0
        if (sessionName != null) count++
        if (notes != null) count++
        if (startTime != null) count++
        if (endTime != null) count++
        if (totalDuration != null) count++
        if (isCompleted != null) count++
        if (exerciseUpdates != null) count++
        return count
    }
}