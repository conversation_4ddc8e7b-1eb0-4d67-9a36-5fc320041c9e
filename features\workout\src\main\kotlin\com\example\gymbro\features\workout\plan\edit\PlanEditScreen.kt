package com.example.gymbro.features.workout.plan.edit

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.designSystem.components.ButtonImportance
import com.example.gymbro.designSystem.components.GymBroButton
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.plan.PlanContract
import com.example.gymbro.features.workout.plan.PlanViewModel
import com.example.gymbro.features.workout.plan.canvas.components.*
import com.example.gymbro.features.workout.plan.canvas.coordinator.UnifiedDragCoordinator
import com.example.gymbro.features.workout.plan.canvas.model.*
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.TemplateViewModel
import kotlinx.collections.immutable.toImmutableList

/**
 * 新版画布式训练计划编辑屏幕
 * 
 * 🎯 核心特性：
 * - 统一画布设计：4周x7天网格布局
 * - 跨模块拖拽：Template和TemplateDraft的统一支持
 * - M3动画效果：流畅的拖拽和放置动画
 * - 事件驱动架构：解耦的拖拽协调系统
 * - 撤销/重做支持：完整的操作历史管理
 * 
 * 🏗️ 架构升级：
 * - 画布数据模型：PlanCanvasData统一数据管理
 * - 拖拽协调器：UnifiedDragCoordinator统一控制
 * - 组件化设计：高度复用的UI组件系统
 * - 性能优化：LazyColumn虚拟化和智能重组
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PlanEditScreen(
    planId: String?,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    planViewModel: PlanViewModel = hiltViewModel(),
    templateViewModel: TemplateViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    
    // State监听
    val planState by planViewModel.state.collectAsStateWithLifecycle()
    val templateState by templateViewModel.state.collectAsStateWithLifecycle()
    
    // 拖拽协调器
    val dragCoordinator = remember { UnifiedDragCoordinator() }
    
    // 画布数据状态
    var canvasData by remember {
        mutableStateOf(
            PlanCanvasData.createEmpty(
                planName = if (planId != null) "编辑计划" else "新建计划"
            )
        )
    }
    
    // 可拖拽数据源
    val draggableTemplates = remember(templateState.templates) {
        templateState.templates.map { templateDto ->
            DraggableItemData.TemplateData.fromTemplateDto(templateDto)
        }
    }
    
    // 这里需要集成TemplateDraft数据源
    // val draggableDrafts = remember(templateState.drafts) { ... }
    
    // 初始化画布
    LaunchedEffect(planId, planState.selectedPlan) {
        planState.selectedPlan?.let { plan ->
            // 将现有计划数据转换为画布数据
            val convertedCanvasData = convertPlanToCanvas(plan, canvasData)
            canvasData = convertedCanvasData
            dragCoordinator.initializeCanvas(convertedCanvasData)
        } ?: run {
            // 新建计划模式
            dragCoordinator.initializeCanvas(canvasData)
        }
    }
    
    // 初始化数据加载
    LaunchedEffect(Unit) {
        if (planId != null) {
            planViewModel.selectPlan(planId)
        }
        templateViewModel.loadTemplates()
        // TODO: 加载TemplateDraft数据
        // templateViewModel.loadDrafts()
    }
    
    // 拖拽事件监听
    LaunchedEffect(dragCoordinator) {
        dragCoordinator.addDragEventListener(object : UnifiedDragCoordinator.DragEventListener {
            override fun onDrop(item: UnifiedDragCoordinator.DraggableItemData, dropZone: UnifiedDragCoordinator.DropZone) {
                // 处理拖拽成功事件
                // 可以触发触觉反馈、显示成功提示等
            }
            
            override fun onDragCancel(item: UnifiedDragCoordinator.DraggableItemData) {
                // 处理拖拽取消事件
            }
        })
    }
    
    Scaffold(
        topBar = {
            CanvasTopBar(
                title = canvasData.planName,
                onNavigateBack = onNavigateBack,
                onSave = {
                    // 保存画布数据到Plan
                    savePlanFromCanvas(canvasData, planViewModel, planId)
                },
                canUndo = canvasData.canUndo(),
                canRedo = canvasData.canRedo(),
                onUndo = { dragCoordinator.undo() },
                onRedo = { dragCoordinator.redo() }
            )
        },
        modifier = modifier
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 主画布区域
            Row(
                modifier = Modifier.fillMaxSize(),
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium)
            ) {
                // 左侧：拖拽源面板
                DragSourcePanel(
                    templates = draggableTemplates,
                    drafts = emptyList(), // TODO: 添加草稿数据
                    coordinator = dragCoordinator,
                    modifier = Modifier
                        .width(300.dp)
                        .fillMaxHeight()
                        .padding(Tokens.Spacing.Medium)
                )
                
                // 右侧：画布区域
                DropZoneCanvas(
                    canvasData = canvasData,
                    coordinator = dragCoordinator,
                    onCanvasUpdated = { updatedCanvas ->
                        canvasData = updatedCanvas
                    },
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight()
                        .padding(Tokens.Spacing.Medium)
                )
            }
        }
    }
}

/**
 * 画布顶部栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CanvasTopBar(
    title: String,
    onNavigateBack: () -> Unit,
    onSave: () -> Unit,
    canUndo: Boolean,
    canRedo: Boolean,
    onUndo: () -> Unit,
    onRedo: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Text(
                text = title,
                color = MaterialTheme.workoutColors.textPrimary
            )
        },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = MaterialTheme.workoutColors.textPrimary
                )
            }
        },
        actions = {
            // 撤销按钮
            IconButton(
                onClick = onUndo,
                enabled = canUndo
            ) {
                Icon(
                    imageVector = Icons.Default.Save, // 替换为撤销图标
                    contentDescription = "撤销",
                    tint = if (canUndo) {
                        MaterialTheme.workoutColors.accentPrimary
                    } else {
                        MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.5f)
                    }
                )
            }
            
            // 重做按钮
            IconButton(
                onClick = onRedo,
                enabled = canRedo
            ) {
                Icon(
                    imageVector = Icons.Default.Save, // 替换为重做图标
                    contentDescription = "重做",
                    tint = if (canRedo) {
                        MaterialTheme.workoutColors.accentPrimary
                    } else {
                        MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.5f)
                    }
                )
            }
            
            // 保存按钮
            GymBroButton(
                text = UiText.DynamicString("保存"),
                onClick = onSave,
                importance = ButtonImportance.Primary,
                modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium)
            )
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.background
        ),
        modifier = modifier
    )
}

/**
 * 拖拽源面板
 */
@Composable
private fun DragSourcePanel(
    templates: List<DraggableItemData.TemplateData>,
    drafts: List<DraggableItemData.DraftData>,
    coordinator: UnifiedDragCoordinator,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.workoutColors.cardBackground
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = Tokens.Elevation.Medium
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium)
        ) {
            // 面板标题
            Text(
                text = "拖拽源",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.workoutColors.textPrimary
            )
            
            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))
            
            // Tab选择器（Template/Draft切换）
            var selectedTab by remember { mutableIntStateOf(0) }
            
            TabRow(
                selectedTabIndex = selectedTab,
                contentColor = MaterialTheme.workoutColors.accentPrimary,
                containerColor = MaterialTheme.workoutColors.cardBackground
            ) {
                Tab(
                    selected = selectedTab == 0,
                    onClick = { selectedTab = 0 },
                    text = {
                        Text(
                            text = "模板 (${templates.size})",
                            color = if (selectedTab == 0) {
                                MaterialTheme.workoutColors.accentPrimary
                            } else {
                                MaterialTheme.workoutColors.textSecondary
                            }
                        )
                    }
                )
                
                Tab(
                    selected = selectedTab == 1,
                    onClick = { selectedTab = 1 },
                    text = {
                        Text(
                            text = "草稿 (${drafts.size})",
                            color = if (selectedTab == 1) {
                                MaterialTheme.workoutColors.accentSecondary
                            } else {
                                MaterialTheme.workoutColors.textSecondary
                            }
                        )
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))
            
            // 拖拽项列表
            when (selectedTab) {
                0 -> {
                    // Template列表
                    DragSourceList(
                        items = templates,
                        coordinator = coordinator,
                        emptyMessage = "暂无可用模板"
                    )
                }
                1 -> {
                    // Draft列表
                    DragSourceList(
                        items = drafts,
                        coordinator = coordinator,
                        emptyMessage = "暂无草稿"
                    )
                }
            }
        }
    }
}

/**
 * 拖拽源列表
 */
@Composable
private fun <T : DraggableItemData> DragSourceList(
    items: List<T>,
    coordinator: UnifiedDragCoordinator,
    emptyMessage: String,
    modifier: Modifier = Modifier
) {
    if (items.isEmpty()) {
        // 空状态
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = emptyMessage,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.workoutColors.textSecondary
            )
        }
    } else {
        // 拖拽项列表
        Column(
            modifier = modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
        ) {
            items.forEach { item ->
                UnifiedDraggableItem(
                    itemData = item,
                    coordinator = coordinator,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

// === 数据转换辅助函数 ===

/**
 * 将现有Plan数据转换为画布数据
 */
private fun convertPlanToCanvas(
    plan: com.example.gymbro.domain.workout.model.WorkoutPlan,
    baseCanvas: PlanCanvasData
): PlanCanvasData {
    val scheduleItems = mutableMapOf<Int, List<CanvasItem>>()
    
    // 转换dailySchedule为CanvasItem
    plan.dailySchedule.forEach { (dayNumber, dayPlan) ->
        val canvasItems = mutableListOf<CanvasItem>()
        
        // 处理templateVersionIds（如果存在）
        dayPlan.templateVersionIds.forEach { templateVersionId ->
            // TODO: 这里需要通过templateVersionId获取对应的Template数据
            // 暂时创建一个占位的CanvasItem
            val position = CanvasPosition.fromDayIndex(dayNumber, canvasItems.size)
            val placeholderItem = CanvasItem.CustomItem(
                id = "placeholder_$templateVersionId",
                name = "模板引用",
                position = position,
                metadata = mapOf("templateVersionId" to templateVersionId),
                description = "基于模板版本: $templateVersionId",
                estimatedDuration = null
            )
            canvasItems.add(placeholderItem)
        }
        
        // 处理templateVersionIds（向后兼容）
        dayPlan.templateVersionIds.forEach { templateVersionId ->
            // TODO: 通过templateVersionId获取Template数据并转换
            val position = CanvasPosition.fromDayIndex(dayNumber, canvasItems.size)
            val placeholderItem = CanvasItem.CustomItem(
                id = "template_ref_$templateVersionId",
                name = "模板",
                position = position,
                metadata = mapOf("templateVersionId" to templateVersionId),
                description = "模板ID: $templateVersionId",
                estimatedDuration = null
            )
            canvasItems.add(placeholderItem)
        }
        
        if (canvasItems.isNotEmpty()) {
            scheduleItems[dayNumber] = canvasItems
        }
    }
    
    return baseCanvas.copy(
        planName = plan.name.toString(),
        scheduleItems = scheduleItems
    )
}

/**
 * 保存画布数据到Plan
 */
private fun savePlanFromCanvas(
    canvasData: PlanCanvasData,
    planViewModel: PlanViewModel,
    planId: String?
) {
    // TODO: 实现画布数据到Plan数据的转换
    // 1. 将CanvasItem转换为DayPlan
    // 2. 调用planViewModel.savePlan()
    
    // 暂时的占位实现
    planViewModel.dispatch(PlanContract.Intent.SavePlan)
}