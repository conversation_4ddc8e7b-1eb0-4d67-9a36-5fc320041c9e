package com.example.gymbro.shared.models.workout

import kotlinx.serialization.Serializable

/**
 * 计划更新数据类
 *
 * 用于更新训练计划的部分信息，支持可选字段更新
 * 遵循shared-models模块的纯DTO设计原则，不包含业务逻辑
 *
 * @property planName 计划名称（可选更新）
 * @property description 计划描述（可选更新）
 * @property startDate 开始日期（可选更新）
 * @property endDate 结束日期（可选更新）
 * @property targetWeeksCount 目标周数（可选更新）
 * @property isActive 是否激活（可选更新）
 * @property templateIds 模板ID列表（可选更新）
 * @property weeklySchedule 每周安排（可选更新）
 */
@Serializable
data class PlanUpdateData(
    val planName: String? = null,
    val description: String? = null,
    val startDate: String? = null,
    val endDate: String? = null,
    val targetWeeksCount: Int? = null,
    val isActive: Boolean? = null,
    val templateIds: List<String>? = null,
    val weeklySchedule: Map<String, String>? = null,
) {
    companion object {
        /**
         * 创建空的更新数据
         */
        fun empty(): PlanUpdateData = PlanUpdateData()

        /**
         * 仅更新名称
         */
        fun nameOnly(planName: String): PlanUpdateData = PlanUpdateData(planName = planName)

        /**
         * 仅更新描述
         */
        fun descriptionOnly(description: String): PlanUpdateData = PlanUpdateData(description = description)

        /**
         * 仅更新状态
         */
        fun statusOnly(isActive: Boolean): PlanUpdateData = PlanUpdateData(isActive = isActive)

        /**
         * 更新日期范围
         */
        fun dateRange(startDate: String, endDate: String): PlanUpdateData = 
            PlanUpdateData(startDate = startDate, endDate = endDate)

        /**
         * 更新模板列表
         */
        fun templatesOnly(templateIds: List<String>): PlanUpdateData = PlanUpdateData(templateIds = templateIds)
    }

    /**
     * 检查是否有任何字段需要更新
     */
    fun hasUpdates(): Boolean {
        return planName != null ||
            description != null ||
            startDate != null ||
            endDate != null ||
            targetWeeksCount != null ||
            isActive != null ||
            templateIds != null ||
            weeklySchedule != null
    }

    /**
     * 获取更新字段的数量
     */
    fun getUpdateCount(): Int {
        var count = 0
        if (planName != null) count++
        if (description != null) count++
        if (startDate != null) count++
        if (endDate != null) count++
        if (targetWeeksCount != null) count++
        if (isActive != null) count++
        if (templateIds != null) count++
        if (weeklySchedule != null) count++
        return count
    }
}