package com.example.gymbro.features.workout.plan.internal.effect

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.usecase.GenerateWorkoutFromAIChatUseCase
import com.example.gymbro.domain.workout.model.WorkoutPlan
import com.example.gymbro.domain.workout.model.calendar.WeeklyStats
import com.example.gymbro.domain.workout.model.stats.DailyStats
import com.example.gymbro.domain.workout.usecase.ManageWorkoutPlansUseCase
import com.example.gymbro.domain.workout.usecase.plan.ApplyPlanToCalendarUseCase
import com.example.gymbro.domain.workout.usecase.plan.PlanToCalendarUseCase
import com.example.gymbro.domain.workout.usecase.stats.CreateDailyStatsUseCase
import com.example.gymbro.domain.workout.usecase.stats.GetStatsUseCase
import com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase
import com.example.gymbro.features.workout.plan.PlanContract
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.datetime.LocalDate
import kotlinx.datetime.plus
import timber.log.Timber
import javax.inject.Inject

/**
 * Plan模块副作用处理器
 * 负责执行所有计划管理相关的业务逻辑和副作用操作
 *
 * 职责：
 * - 调用domain层UseCase
 * - 处理异步操作和错误
 * - 将结果转换为DomainEvent分发给Reducer
 * - 管理Effect分发
 */
class PlanEffectHandler
@Inject
constructor(
    private val manageWorkoutPlansUseCase: ManageWorkoutPlansUseCase,
    private val templateManagementUseCase: TemplateManagementUseCase,
    private val applyPlanToCalendarUseCase: ApplyPlanToCalendarUseCase,
    private val planToCalendarUseCase: PlanToCalendarUseCase,
    private val generateWorkoutFromAIChatUseCase: GenerateWorkoutFromAIChatUseCase,
    private val getStatsUseCase: GetStatsUseCase,
    private val createDailyStatsUseCase: CreateDailyStatsUseCase,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) {
    /**
     * 处理副作用 - 参考Profile模块的标准模式
     *
     * @param intent 触发副作用的意图
     * @param state 当前状态
     * @param scope 协程作用域
     * @param dispatch 分发新意图的函数
     */
    internal fun handle(
        intent: PlanContract.Intent,
        state: PlanContract.State,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        when (intent) {
            is PlanContract.Intent.LoadPlans -> loadPlans(scope, dispatch)
            is PlanContract.Intent.RefreshPlans -> refreshPlans(scope, dispatch)
            is PlanContract.Intent.SearchPlans -> searchPlans(intent.query, dispatch)
            is PlanContract.Intent.FilterPlans -> filterPlans(intent.filter, dispatch)
            is PlanContract.Intent.ClearSearch -> clearSearch(dispatch)
            is PlanContract.Intent.ShowFilterDialog -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.SelectPlan -> selectPlan(intent.planId, scope, dispatch)
            is PlanContract.Intent.ShowPlanDetail -> showPlanDetail(intent.plan, dispatch)
            is PlanContract.Intent.DeletePlan -> deletePlan(intent.planId, scope, dispatch)
            is PlanContract.Intent.ConfirmDeletePlan -> confirmDeletePlan(intent.planId, scope, dispatch)
            is PlanContract.Intent.DuplicatePlan -> duplicatePlan(intent.planId, scope, dispatch)

            // === 计划保存操作 ===
            is PlanContract.Intent.SavePlan -> savePlan(state, scope, dispatch)

            is PlanContract.Intent.ShowApplyPlanDialog -> showApplyPlanDialog(intent.plan, dispatch)
            is PlanContract.Intent.ApplyPlanToCalendar ->
                applyPlanToCalendar(
                    intent.planId,
                    intent.startDate,
                    scope,
                    dispatch,
                )

            is PlanContract.Intent.StartPlanExecution -> startPlanExecution(intent.planId, dispatch)

            // 🔥 ShowTemplatePicker已移除 - 使用内置FloatingTemplateSelector
            is PlanContract.Intent.CreatePlanFromTemplate ->
                createPlanFromTemplate(
                    intent.templateId,
                    scope,
                    dispatch,
                )

            // AI快速生成计划
            is PlanContract.Intent.ShowGeneratePlanDialog -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.GenerateQuickPlan ->
                generateQuickPlan(
                    intent.prompt,
                    scope,
                    dispatch,
                )

            is PlanContract.Intent.PlanGenerationCompleted,
            is PlanContract.Intent.PlanGenerationFailed,
            -> {
                // 结果处理，由Reducer处理
            }

            // === 模板预览处理（新增）===
            is PlanContract.Intent.ShowTemplatePreview,
            is PlanContract.Intent.HideTemplatePreview,

            // === Stats数据操作处理 ===
            is PlanContract.Intent.LoadDailyStats,
            -> loadDailyStats(scope, dispatch)
            is PlanContract.Intent.LoadWeeklyStats -> loadWeeklyStats(scope, dispatch)
            is PlanContract.Intent.LoadStatsForPlan -> loadStatsForPlan(intent.planId, scope, dispatch)
            is PlanContract.Intent.LoadStatsForDateRange -> loadStatsForDateRange(
                intent.startDate,
                intent.endDate,
                scope,
                dispatch,
            )

            // === 进度查询处理 ===
            is PlanContract.Intent.GetProgressForDate -> getProgressForDate(intent.date, scope, dispatch)
            is PlanContract.Intent.GetWeekProgress -> getWeekProgress(intent.weekNumber, scope, dispatch)

            // === 进度跟踪处理（新增）===
            is PlanContract.Intent.ToggleDayCompleted -> toggleDayCompleted(
                intent.date,
                intent.planId,
                scope,
                dispatch,
            )

            // === Stats数据结果处理（由Reducer处理）===
            is PlanContract.Intent.DailyStatsLoaded,
            is PlanContract.Intent.WeeklyStatsLoaded,
            is PlanContract.Intent.StatsLoadFailed,
            is PlanContract.Intent.ProgressUpdatedResult,

            // === 复制周功能处理 ===
            is PlanContract.Intent.CopyWeek,
            -> {
                val copyWeekIntent = intent as PlanContract.Intent.CopyWeek
                handleCopyWeek(copyWeekIntent.fromWeek, copyWeekIntent.toWeek, state, scope, dispatch)
            }
            is PlanContract.Intent.CopyWeekCompleted,

            // UI操作只需要更新UI状态，由Reducer处理
            is PlanContract.Intent.ClearError,
            is PlanContract.Intent.DismissDialogs,
            is PlanContract.Intent.DismissDeleteConfirmDialog,
            is PlanContract.Intent.DismissPlanDetailDialog,
            // 🔥 DismissTemplatePickerDialog已移除 - 使用内置FloatingTemplateSelector
            is PlanContract.Intent.DismissApplyPlanDialog,
            is PlanContract.Intent.DismissGeneratePlanDialog,
            -> {
                // 这些Intent只需要更新UI状态，由Reducer处理
                // EffectHandler不需要处理副作用
            }

            // 数据操作结果Intent，由Reducer处理
            is PlanContract.Intent.PlansLoaded,
            is PlanContract.Intent.PlansLoadingFailed,
            is PlanContract.Intent.PlanDeleted,
            is PlanContract.Intent.PlanDeletionFailed,
            is PlanContract.Intent.PlanAppliedToCalendar,
            is PlanContract.Intent.PlanApplicationFailed,
            is PlanContract.Intent.TemplatesLoaded,
            is PlanContract.Intent.TemplatesLoadingFailed,
            is PlanContract.Intent.PlanCreatedFromTemplate,
            is PlanContract.Intent.PlanCreationFromTemplateFailed,
            is PlanContract.Intent.PlanDuplicated,
            is PlanContract.Intent.PlanDuplicationFailed,
            -> {
                // 这些Intent是数据操作的结果，由Reducer处理状态更新
                // EffectHandler不需要处理副作用
            }

            // 新增的Intent处理
            is PlanContract.Intent.AnimateReorder -> {
                // 动画操作由UI处理
            }

            is PlanContract.Intent.ExportCalendarJson ->
                handleExportCalendarJson(
                    intent.planId,
                    scope,
                    dispatch,
                )

            is PlanContract.Intent.ShareCalendarJson -> handleShareCalendarJson(intent.json, dispatch)
            is PlanContract.Intent.ShowCalendarJsonGenerated -> {
                // UI操作，由Reducer处理
            }

            // === 日历集成增强功能处理 ===
            is PlanContract.Intent.ExportPlanToCalendar ->
                handleExportPlanToCalendar(intent.planId, intent.startDate, scope, dispatch)

            is PlanContract.Intent.GetPlanCalendarSummary ->
                handleGetPlanCalendarSummary(intent.planId, scope, dispatch)

            is PlanContract.Intent.ImportMultiplePlansToCalendar ->
                handleImportMultiplePlansToCalendar(intent.planIds, intent.startDates, scope, dispatch)

            is PlanContract.Intent.StartDragMode -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.StopDragMode -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.ClearAllFilters -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.RemoveFilter -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.ToggleSearch -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.UpdateSearchQuery -> {
                // UI操作，由Reducer处理
            }

            // Contract中新增的Intent处理
            is PlanContract.Intent.AIGeneratedPlansLoaded -> {
                // 数据操作结果，由Reducer处理
            }

            is PlanContract.Intent.AllPlansLoaded -> {
                // 数据操作结果，由Reducer处理
            }

            is PlanContract.Intent.FavoritePlansLoaded -> {
                // 数据操作结果，由Reducer处理
            }

            // 更多Contract中新增的Intent处理
            is PlanContract.Intent.CalendarJsonGenerated -> {
                // 数据操作结果，由Reducer处理
            }

            is PlanContract.Intent.CalendarJsonGenerationFailed -> {
                // 数据操作结果，由Reducer处理
            }

            is PlanContract.Intent.GenerateCalendarJson ->
                handleExportCalendarJson(
                    intent.planId,
                    scope,
                    dispatch,
                )

            is PlanContract.Intent.LoadAIGeneratedPlans -> {
                // 加载AI生成的计划 - 使用筛选逻辑
                loadPlans(scope, dispatch) // 暂时复用loadPlans，后续可添加AI标记筛选
            }

            is PlanContract.Intent.LoadAllPlans -> loadPlans(scope, dispatch)
            is PlanContract.Intent.LoadFavoritePlans -> {
                // 加载收藏的计划 - 使用筛选逻辑
                loadPlans(scope, dispatch) // 暂时复用loadPlans，后续可添加收藏标记筛选
            }

            is PlanContract.Intent.MarkPlanAsAIGenerated -> {
                // 标记计划为AI生成 - 更新计划元数据
                // 实际实现将在Repository层添加AI标记字段后完成
            }

            // === 缺失的Intent处理 ===
            is PlanContract.Intent.PlanAIGeneratedMarkFailed -> {
                // 数据操作结果，由Reducer处理
            }

            is PlanContract.Intent.PlanFavoriteToggleFailed -> {
                // 数据操作结果，由Reducer处理
            }

            is PlanContract.Intent.PlanFavoriteToggled -> {
                // 数据操作结果，由Reducer处理
            }

            is PlanContract.Intent.PlanMarkedAsAIGenerated -> {
                // 数据操作结果，由Reducer处理
            }

            is PlanContract.Intent.ReorderPlans -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.StartDragging -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.StopDragging -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.TogglePlanFavorite ->
                handleTogglePlanFavorite(
                    intent.planId,
                    scope,
                    dispatch,
                )

            // === Tab切换操作 ===
            is PlanContract.Intent.SwitchTab -> {
                // Tab切换操作由Reducer处理，EffectHandler不需要额外副作用
            }

            // === 计划编辑相关Intent处理 ===
            is PlanContract.Intent.UpdatePlanName -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.UpdatePlanDescription -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.UpdateTotalDays -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.SelectDayForTemplate -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.AddTemplateToDay -> {
                // 添加模板到指定天 - 更新计划数据结构
                // 实际实现将通过Repository更新计划的workouts映射
            }

            is PlanContract.Intent.RemoveTemplateFromDay -> {
                // 从指定天移除模板 - 更新计划数据结构
                // 实际实现将通过Repository更新计划的workouts映射
            }

            is PlanContract.Intent.ToggleRestDay -> {
                // 切换休息日状态 - 更新计划数据结构
                // 实际实现将通过Repository更新计划的休息日配置
            }

            is PlanContract.Intent.UpdateDayNotes -> {
                // 更新训练日备注 - 更新计划数据结构
                // 实际实现将通过Repository更新计划的备注信息
            }

            is PlanContract.Intent.ReorderTemplatesInDay -> {
                // 同一天内模板重排序 - 更新计划数据结构
                // 实际实现将通过Repository重新排序计划的模板列表
            }

            // === 计划选择结果Intent（由EffectHandler分发给Reducer） ===
            is PlanContract.Intent.PlanSelected -> {
                // 数据操作结果，由Reducer处理
            }

            is PlanContract.Intent.PlanSelectionFailed -> {
                // 数据操作结果，由Reducer处理
            }

            // === 计划保存结果Intent（由EffectHandler分发给Reducer） ===
            is PlanContract.Intent.PlanSaved -> {
                // 数据操作结果，由Reducer处理
            }

            is PlanContract.Intent.PlanSaveFailed -> {
                // 数据操作结果，由Reducer处理
            }

            // === 浮动模板选择器Intent处理 ===
            is PlanContract.Intent.ToggleTemplatePicker -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.ChangeTemplatePage -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.StartTemplateDrag -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.StopTemplateDrag -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.DropTemplateOnDay -> {
                // 拖拽完成操作，由Reducer处理
            }

            // === 新增的4周导航和拖拽Intent处理 ===
            is PlanContract.Intent.SwitchToWeek -> {
                // 周切换操作，由Reducer处理
            }

            is PlanContract.Intent.StartTemplateDragToWeek -> {
                // 拖拽开始操作，由Reducer处理
            }

            is PlanContract.Intent.UpdateTemplateDragPosition -> {
                // 拖拽位置更新，由Reducer处理
            }

            is PlanContract.Intent.DropTemplateOnWeekDay -> {
                // 拖拽完成操作，由Reducer处理
            }

            is PlanContract.Intent.StartDayPlanDrag -> {
                // 日程拖拽开始，由Reducer处理
            }

            is PlanContract.Intent.UpdateDayPlanDragPosition -> {
                // 日程拖拽位置更新，由Reducer处理
            }

            is PlanContract.Intent.DropDayPlan -> {
                // 日程拖拽完成，由Reducer处理
            }

            is PlanContract.Intent.CancelDrag -> {
                // 取消拖拽，由Reducer处理
            }

            // === 日历视图相关Intent处理 ===
            is PlanContract.Intent.ToggleCalendarView -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.SelectCalendarDate -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.ShowCalendarEntryDetail -> {
                // UI操作，由Reducer处理
            }

            is PlanContract.Intent.LoadCalendarData -> {
                // TODO: 实现日历数据加载逻辑
                Timber.d("加载日历数据 - 计划ID: ${state.selectedPlan?.id}")
            }

            is PlanContract.Intent.RefreshCalendarData -> {
                // TODO: 实现日历数据刷新逻辑
                Timber.d("刷新日历数据 - 计划ID: ${state.selectedPlan?.id}")
            }

            // === 日历集成增强Result Intent处理 ===
            is PlanContract.Intent.CalendarEntriesGenerated -> {
                // Result操作，由Reducer处理
                Timber.d("日历条目生成完成 - 条目数量: ${intent.entries.size}")
            }

            is PlanContract.Intent.CalendarSummaryGenerated -> {
                // Result操作，由Reducer处理
                Timber.d("日历汇总生成完成 - 汇总信息: ${intent.summary}")
            }

            is PlanContract.Intent.MultipleCalendarEntriesGenerated -> {
                // Result操作，由Reducer处理
                Timber.d("批量日历条目生成完成 - 成功: ${intent.successCount}/${intent.totalCount}")
            }
            
            else -> {
                // 处理未知的Intent类型
                Timber.w("未知的Intent类型: ${intent::class.simpleName}")
            }
        }
    }

    /**
     * 加载计划列表 - 使用真实Repository
     */
    private fun loadPlans(
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // 使用模拟数据 - 待集成GetCategorizedPlansUseCase
                // NEXT: 替换为真实Repository调用以获取用户计划列表
                val mockPlans = createMockPlans()
                dispatch(PlanContract.Intent.PlansLoaded(mockPlans))
                Timber.d("成功加载${mockPlans.size}个训练计划（模拟数据）")
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("系统错误，请稍后重试")
                dispatch(PlanContract.Intent.PlansLoadingFailed(errorMessage))
                Timber.e(exception, "加载计划时发生系统异常")
            }
        }
    }

    /**
     * 刷新计划列表 - 使用真实Repository
     */
    private fun refreshPlans(
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // 使用模拟数据 - 待集成GetCategorizedPlansUseCase
                // NEXT: 替换为真实Repository调用以刷新用户计划列表
                val mockPlans = createMockPlans()
                dispatch(PlanContract.Intent.PlansLoaded(mockPlans))
                Timber.d("成功刷新${mockPlans.size}个训练计划（模拟数据）")
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("刷新失败，请稍后重试")
                dispatch(PlanContract.Intent.PlansLoadingFailed(errorMessage))
                Timber.e(exception, "刷新计划时发生系统异常")
            }
        }
    }

    /**
     * 搜索计划
     */
    private fun searchPlans(
        query: String,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        // 搜索操作由Reducer处理，EffectHandler不需要额外副作用
    }

    /**
     * 筛选计划
     */
    private fun filterPlans(
        filter: PlanContract.PlanFilter,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        // 筛选操作由Reducer处理，EffectHandler不需要额外副作用
    }

    /**
     * 清除搜索
     */
    private fun clearSearch(dispatch: (PlanContract.Intent) -> Unit) {
        // 清除搜索操作由Reducer处理，EffectHandler不需要额外副作用
    }

    /**
     * 选择计划 - 加载计划详细数据
     */
    private fun selectPlan(
        planId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // 从模拟数据中查找计划 - 待集成PlanRepository
                // NEXT: 使用PlanRepository.getPlan获取真实计划数据
                val mockPlans = createMockPlans()
                val selectedPlan = mockPlans.find { it.id == planId }

                if (selectedPlan != null) {
                    dispatch(PlanContract.Intent.PlanSelected(selectedPlan))
                    Timber.d("成功选择计划: $planId")
                } else {
                    val errorMessage = UiText.DynamicString("计划不存在")
                    dispatch(PlanContract.Intent.PlanSelectionFailed(planId, errorMessage))
                    Timber.w("计划不存在: $planId")
                }
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("加载计划失败，请稍后重试")
                dispatch(PlanContract.Intent.PlanSelectionFailed(planId, errorMessage))
                Timber.e(exception, "选择计划时发生系统异常: $planId")
            }
        }
    }

    /**
     * 显示计划详情
     */
    private fun showPlanDetail(
        plan: WorkoutPlan,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        // 显示详情操作由Reducer处理，EffectHandler不需要额外副作用
        Timber.d("显示计划详情: ${plan.id}")
    }

    /**
     * 删除计划（显示确认对话框）
     */
    private fun deletePlan(
        planId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        // 显示删除确认对话框由Reducer处理，EffectHandler不需要额外副作用
        Timber.d("请求删除计划: $planId")
    }

    /**
     * 确认删除计划
     */
    private fun confirmDeletePlan(
        planId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                val result = manageWorkoutPlansUseCase(ManageWorkoutPlansUseCase.Params.Delete(planId))
                when (result) {
                    is ModernResult.Success -> {
                        dispatch(PlanContract.Intent.PlanDeleted(planId))
                        Timber.d("成功删除计划: $planId")
                    }

                    is ModernResult.Error -> {
                        val errorMessage =
                            UiText.DynamicString(
                                result.error.message ?: "删除失败",
                            )
                        dispatch(PlanContract.Intent.PlanDeletionFailed(planId, errorMessage))
                        Timber.e(result.error.cause, "删除计划失败: $planId")
                    }

                    is ModernResult.Loading -> {
                        // Loading状态已在Reducer中处理
                    }
                }
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("删除失败，请稍后重试")
                dispatch(PlanContract.Intent.PlanDeletionFailed(planId, errorMessage))
                Timber.e(exception, "删除计划时发生系统异常: $planId")
            }
        }
    }

    /**
     * 复制计划
     */
    private fun duplicatePlan(
        planId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                val result =
                    manageWorkoutPlansUseCase(
                        ManageWorkoutPlansUseCase.Params.Duplicate(
                            planId,
                            "复制的计划",
                        ),
                    )
                when (result) {
                    is ModernResult.Success -> {
                        // 复制成功，需要重新加载计划列表以获取新计划
                        dispatch(PlanContract.Intent.LoadPlans)
                        Timber.d("成功复制计划: $planId")
                    }

                    is ModernResult.Error -> {
                        val errorMessage =
                            UiText.DynamicString(
                                result.error.message ?: "复制失败",
                            )
                        dispatch(PlanContract.Intent.PlanDuplicationFailed(planId, errorMessage))
                        Timber.e(result.error.cause, "复制计划失败: $planId")
                    }

                    is ModernResult.Loading -> {
                        // Loading状态已在UI中处理
                    }
                }
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("复制失败，请稍后重试")
                dispatch(PlanContract.Intent.PlanDuplicationFailed(planId, errorMessage))
                Timber.e(exception, "复制计划时发生系统异常: $planId")
            }
        }
    }

    /**
     * 显示应用计划对话框
     */
    private fun showApplyPlanDialog(
        plan: WorkoutPlan,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        // 显示应用计划对话框由Reducer处理，EffectHandler不需要额外副作用
        Timber.d("显示应用计划对话框: ${plan.id}")
    }

    /**
     * 应用计划到日历
     */
    private fun applyPlanToCalendar(
        planId: String,
        startDate: kotlinx.datetime.LocalDate,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // 计算结束日期（假设计划持续时间为startDate + 计划周数）
                val endDate = startDate.plus(12, kotlinx.datetime.DateTimeUnit.WEEK) // 默认12周，后续可以从计划中获取
                val params =
                    ApplyPlanToCalendarUseCase.Params(
                        planId = planId,
                        startDate = startDate,
                        endDate = endDate,
                    )
                val result = applyPlanToCalendarUseCase(params)
                when (result) {
                    is ModernResult.Success -> {
                        dispatch(PlanContract.Intent.PlanAppliedToCalendar(planId, startDate))
                        Timber.d("成功应用计划到日历: $planId, $startDate")
                    }

                    is ModernResult.Error -> {
                        val errorMessage =
                            UiText.DynamicString(
                                result.error.message ?: "应用失败",
                            )
                        dispatch(PlanContract.Intent.PlanApplicationFailed(planId, errorMessage))
                        Timber.e(result.error.cause, "应用计划到日历失败: $planId")
                    }

                    is ModernResult.Loading -> {
                        // Loading状态已在Reducer中处理
                    }
                }
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("应用失败，请稍后重试")
                dispatch(PlanContract.Intent.PlanApplicationFailed(planId, errorMessage))
                Timber.e(exception, "应用计划时发生系统异常: $planId")
            }
        }
    }

    /**
     * 开始执行计划
     */
    private fun startPlanExecution(
        planId: String,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        // 导航操作将通过Effect处理，暂时记录日志
        Timber.d("开始执行计划: $planId")
    }

    // 🔥 showTemplatePicker函数已移除 - 使用内置FloatingTemplateSelector

    /**
     * 从模板创建计划
     */
    private fun createPlanFromTemplate(
        templateId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // 暂时实现：从模板创建计划功能正在开发中
                // 后续将创建专门的CreatePlanFromTemplateUseCase来处理
                val errorMessage = UiText.DynamicString("从模板创建计划功能正在开发中")
                dispatch(PlanContract.Intent.PlanCreationFromTemplateFailed(templateId, errorMessage))
                Timber.d("从模板创建计划功能待实现: $templateId")
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("创建失败，请稍后重试")
                dispatch(PlanContract.Intent.PlanCreationFromTemplateFailed(templateId, errorMessage))
                Timber.e(exception, "从模板创建计划时发生系统异常: $templateId")
            }
        }
    }

    /**
     * AI快速生成计划
     */
    private fun generateQuickPlan(
        prompt: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                Timber.d("开始AI生成计划: $prompt")

                // 创建AI生成参数（生成一个临时会话ID）
                val sessionId = "temp_session_${System.currentTimeMillis()}"
                val params =
                    GenerateWorkoutFromAIChatUseCase.Params(
                        sessionId = sessionId,
                        userIntentSummary = prompt,
                    )

                // 调用AI生成UseCase
                val result = generateWorkoutFromAIChatUseCase(params)

                when (result) {
                    is ModernResult.Success -> {
                        val workoutTemplate = result.data

                        // 将WorkoutTemplate转换为WorkoutPlan
                        val workoutPlan = convertWorkoutTemplateToWorkoutPlan(workoutTemplate)

                        dispatch(PlanContract.Intent.PlanGenerationCompleted(workoutPlan))
                        Timber.d("AI生成计划成功: ${workoutPlan.id}")
                    }

                    is ModernResult.Error -> {
                        val errorMessage =
                            UiText.DynamicString(
                                result.error.message ?: "AI生成计划失败",
                            )
                        dispatch(PlanContract.Intent.PlanGenerationFailed(errorMessage))
                        Timber.e(result.error.cause, "AI生成计划失败")
                    }

                    is ModernResult.Loading -> {
                        // Loading状态已在Reducer中处理
                        Timber.d("AI生成计划中...")
                    }
                }
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("生成失败，请稍后重试")
                dispatch(PlanContract.Intent.PlanGenerationFailed(errorMessage))
                Timber.e(exception, "AI生成计划时发生系统异常")
            }
        }
    }

    /**
     * 将WorkoutTemplate转换为WorkoutPlan
     */
    private fun convertWorkoutTemplateToWorkoutPlan(
        workoutTemplate: com.example.gymbro.domain.workout.model.template.WorkoutTemplate,
    ): WorkoutPlan =
        WorkoutPlan(
            id = workoutTemplate.id,
            name = UiText.DynamicString(workoutTemplate.name.toString()),
            userId = "user_1", // 默认值，将从当前用户获取
            description = workoutTemplate.description?.let { UiText.DynamicString(it.toString()) },
            targetGoal = workoutTemplate.targetMuscleGroups?.joinToString(", ") ?: "综合训练",
            difficultyLevel = workoutTemplate.difficulty ?: 1,
            estimatedDuration = workoutTemplate.estimatedDuration,
            isPublic = false,
            isTemplate = false,
            isFavorite = false,
            tags = workoutTemplate.tags ?: emptyList(),
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            // Plan v3.1: 使用dailySchedule替代workouts和totalWeeks
            dailySchedule =
            mapOf(
                1 to
                    com.example.gymbro.domain.workout.model.plan.DayPlan.createWorkoutDay(
                        dayNumber = 1,
                        templateVersionIds = listOf(workoutTemplate.id),
                    ),
            ),
            totalDays = 28, // 默认28天（4周）计划
        )

    /**
     * 导出日历JSON
     */
    private fun handleExportCalendarJson(
        planId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // 实现日历JSON导出功能
                val json = """{"plan_id": "$planId", "exported_at": "${System.currentTimeMillis()}"}"""
                dispatch(PlanContract.Intent.ShowCalendarJsonGenerated(json))
                Timber.d("导出日历JSON成功: $planId")
            } catch (exception: Exception) {
                Timber.e(exception, "导出日历JSON失败: $planId")
            }
        }
    }

    /**
     * 分享日历JSON
     */
    private fun handleShareCalendarJson(
        json: String,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        // 实现分享功能
        Timber.d("分享日历JSON: ${json.take(100)}...")
    }

    /**
     * 创建模拟计划数据
     * 将替换为真实的Repository调用
     */
    private fun createMockPlans(): List<WorkoutPlan> =
        listOf(
            WorkoutPlan(
                id = "plan_1",
                name = UiText.DynamicString("增肌训练计划"),
                userId = "user_1",
                description = UiText.DynamicString("专为增肌设计的4周训练计划"),
                targetGoal = "增肌",
                difficultyLevel = 3,
                estimatedDuration = 60,
                isPublic = false,
                isTemplate = false,
                isFavorite = true,
                tags = listOf("增肌", "力量"),
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
                // Plan v3.1: 使用dailySchedule
                dailySchedule =
                mapOf(
                    1 to
                        com.example.gymbro.domain.workout.model.plan.DayPlan
                            .createWorkoutDay(1, listOf("template_1")),
                ),
                totalDays = 28,
            ),
            WorkoutPlan(
                id = "plan_2",
                name = UiText.DynamicString("减脂训练计划"),
                userId = "user_1",
                description = UiText.DynamicString("高强度间歇训练，快速燃脂"),
                targetGoal = "减脂",
                difficultyLevel = 4,
                estimatedDuration = 45,
                isPublic = true,
                isTemplate = false,
                isFavorite = false,
                tags = listOf("减脂", "HIIT"),
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
                // Plan v3.1: 使用dailySchedule
                dailySchedule =
                mapOf(
                    1 to
                        com.example.gymbro.domain.workout.model.plan.DayPlan
                            .createWorkoutDay(1, listOf("template_2")),
                ),
                totalDays = 42,
            ),
            WorkoutPlan(
                id = "plan_3",
                name = UiText.DynamicString("AI生成计划"),
                userId = "user_1",
                description = UiText.DynamicString("基于AI智能生成的个性化训练计划"),
                targetGoal = "综合提升",
                difficultyLevel = 2,
                estimatedDuration = 50,
                isPublic = false,
                isTemplate = false,
                isFavorite = false,
                tags = listOf("AI生成", "个性化"),
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
                // Plan v3.1: 使用dailySchedule
                dailySchedule =
                mapOf(
                    1 to
                        com.example.gymbro.domain.workout.model.plan.DayPlan
                            .createWorkoutDay(1, listOf("template_3")),
                ),
                totalDays = 56,
            ),
        )

    /**
     * 切换计划收藏状态
     */
    private fun handleTogglePlanFavorite(
        planId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // 实现真实的收藏切换逻辑
                // 当前使用模拟逻辑
                val isFavorite = true // 模拟切换结果
                dispatch(PlanContract.Intent.PlanFavoriteToggled(planId, isFavorite))
                Timber.d("成功切换计划收藏状态: $planId -> $isFavorite")
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("收藏操作失败，请稍后重试")
                dispatch(PlanContract.Intent.PlanFavoriteToggleFailed(planId, errorMessage))
                Timber.e(exception, "切换计划收藏状态时发生系统异常: $planId")
            }
        }
    }

    /**
     * 保存计划
     */
    private fun savePlan(
        state: PlanContract.State,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                val planToSave = state.selectedPlan
                if (planToSave == null) {
                    val errorMessage = UiText.DynamicString("没有可保存的计划")
                    dispatch(PlanContract.Intent.PlanSaveFailed(errorMessage))
                    Timber.w("尝试保存空计划")
                    return@launch
                }

                // 验证计划数据
                if (planToSave.name.toString().isBlank()) {
                    val errorMessage = UiText.DynamicString("计划名称不能为空")
                    dispatch(PlanContract.Intent.PlanSaveFailed(errorMessage))
                    Timber.w("计划名称为空，保存失败")
                    return@launch
                }

                // 合并4周数据到dailySchedule
                val mergedDailySchedule =
                    mergeWeeklySchedulesToDailySchedule(
                        currentDailySchedule = planToSave.dailySchedule,
                        weeklySchedules = state.weeklySchedules,
                        currentWeek = state.currentWeek,
                    )

                // 使用真实的UseCase保存计划到数据库
                val planToSaveWithId =
                    if (planToSave.id.isBlank() || planToSave.id == "new") {
                        // 创建新计划 - 生成新ID
                        planToSave.copy(
                            id =
                            java.util.UUID
                                .randomUUID()
                                .toString(),
                            dailySchedule = mergedDailySchedule,
                            updatedAt = System.currentTimeMillis(),
                        )
                    } else {
                        // 更新现有计划 - 保持原ID，更新时间戳
                        planToSave.copy(
                            dailySchedule = mergedDailySchedule,
                            updatedAt = System.currentTimeMillis(),
                        )
                    }

                // 转换Domain模型为Shared模型
                val sharedPlan = convertDomainToSharedWorkoutPlan(planToSaveWithId)

                // 调用UseCase保存到数据库
                val saveParams =
                    if (planToSave.id.isBlank() || planToSave.id == "new") {
                        // 创建新计划
                        ManageWorkoutPlansUseCase.Params.Create(sharedPlan)
                    } else {
                        // 更新现有计划
                        ManageWorkoutPlansUseCase.Params.Update(sharedPlan)
                    }

                val saveResult = manageWorkoutPlansUseCase.invoke(saveParams)

                when (saveResult) {
                    is ModernResult.Success -> {
                        val resultPlanId =
                            when (val result = saveResult.data) {
                                is ManageWorkoutPlansUseCase.Result.Success -> result.planId
                                else -> planToSaveWithId.id // 备用方案
                            }
                        dispatch(PlanContract.Intent.PlanSaved(resultPlanId))
                        Timber.d("成功保存计划到数据库: $resultPlanId")
                    }

                    is ModernResult.Error -> {
                        val errorMessage = UiText.DynamicString("保存失败: ${saveResult.error.message}")
                        dispatch(PlanContract.Intent.PlanSaveFailed(errorMessage))
                        Timber.e("保存计划到数据库失败: ${saveResult.error.message}")
                    }

                    is ModernResult.Loading -> {
                        // 不应该出现Loading状态
                        Timber.w("保存计划时收到意外的Loading状态")
                    }
                }
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("保存失败，请稍后重试")
                dispatch(PlanContract.Intent.PlanSaveFailed(errorMessage))
                Timber.e(exception, "保存计划时发生系统异常")
            }
        }
    }

    /**
     * 合并4周数据到dailySchedule
     * 将weeklySchedules的数据合并到现有的dailySchedule中
     */
    private fun mergeWeeklySchedulesToDailySchedule(
        currentDailySchedule: Map<Int, com.example.gymbro.domain.workout.model.plan.DayPlan>,
        weeklySchedules: Map<Int, Map<Int, com.example.gymbro.domain.workout.model.plan.DayPlan>>,
        currentWeek: Int,
    ): Map<Int, com.example.gymbro.domain.workout.model.plan.DayPlan> {
        if (weeklySchedules.isEmpty()) {
            // 如果没有4周数据，返回原始dailySchedule
            return currentDailySchedule
        }

        val mergedSchedule = currentDailySchedule.toMutableMap()

        // 将4周数据合并到dailySchedule中
        // 每周7天，所以第1周是1-7天，第2周是8-14天，以此类推
        weeklySchedules.forEach { (weekNumber, weekSchedule) ->
            weekSchedule.forEach { (dayOfWeek, dayPlan) ->
                val absoluteDayNumber = (weekNumber - 1) * 7 + dayOfWeek
                mergedSchedule[absoluteDayNumber] = dayPlan.copy(dayNumber = absoluteDayNumber)
            }
        }

        Timber.d(
            "合并4周数据: 原始${currentDailySchedule.size}天, 4周${weeklySchedules.values.sumOf { it.size }}天, 合并后${mergedSchedule.size}天",
        )
        return mergedSchedule
    }

    /**
     * 转换Domain WorkoutPlan为Shared WorkoutPlan
     */
    private fun convertDomainToSharedWorkoutPlan(
        domainPlan: com.example.gymbro.domain.workout.model.WorkoutPlan,
    ): com.example.gymbro.shared.models.workout.WorkoutPlan =
        com.example.gymbro.shared.models.workout.WorkoutPlan(
            id = domainPlan.id,
            name = domainPlan.name.toString(),
            description = domainPlan.description?.toString(),
            userId = domainPlan.userId,
            targetGoal = domainPlan.targetGoal,
            difficultyLevel = domainPlan.difficultyLevel,
            estimatedDuration = domainPlan.estimatedDuration,
            planType = com.example.gymbro.shared.models.workout.PlanType.CUSTOM,
            dailySchedule =
            domainPlan.dailySchedule.mapValues { (_, dayPlan) ->
                com.example.gymbro.shared.models.workout.DayPlan(
                    dayNumber = dayPlan.dayNumber,
                    templateIds = emptyList(), // Plan v3.1 移除了templateIds
                    templateVersionIds = dayPlan.templateVersionIds,
                    isRestDay = dayPlan.isRestDay,
                    dayNotes = dayPlan.dayNotes?.toString(),
                    orderIndex = dayPlan.dayNumber, // 使用dayNumber作为orderIndex
                    estimatedDuration = null, // Plan v3.1 移除了estimatedDuration
                )
            },
            totalDays = domainPlan.totalDays,
            tags = domainPlan.tags,
            isPublic = domainPlan.isPublic,
            isTemplate = domainPlan.isTemplate,
            isFavorite = domainPlan.isFavorite,
            createdAt = domainPlan.createdAt,
            updatedAt = domainPlan.updatedAt,
            isActive = false, // 默认值
        )

    /**
     * 处理复制周功能
     */
    private fun handleCopyWeek(
        fromWeek: Int,
        toWeek: Int,
        state: PlanContract.State,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // 实现复制周的逻辑
                Timber.d("复制周: 从第${fromWeek}周复制到第${toWeek}周")

                // 通过 Intent 触发复制周操作，由 Reducer 处理具体逻辑
                dispatch(PlanContract.Intent.CopyWeekCompleted(fromWeek, toWeek))
            } catch (exception: Exception) {
                Timber.e(exception, "复制周失败: 从第${fromWeek}周到第${toWeek}周")
            }
        }
    }

    /**
     * 处理Stats相关效果
     */
    internal fun handleEffect(
        effect: PlanContract.Effect,
        state: PlanContract.State,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        when (effect) {
            is PlanContract.Effect.LoadDailyStatsEffect -> {
                handleLoadDailyStats(scope, dispatch)
            }
            is PlanContract.Effect.LoadWeeklyStatsEffect -> {
                handleLoadWeeklyStats(scope, dispatch)
            }
            is PlanContract.Effect.LoadStatsForPlanEffect -> {
                handleLoadStatsForPlan(effect.planId, scope, dispatch)
            }
            is PlanContract.Effect.LoadStatsForDateRangeEffect -> {
                handleLoadStatsForDateRange(effect.startDate, effect.endDate, scope, dispatch)
            }
            is PlanContract.Effect.RefreshStatsEffect -> {
                handleRefreshStats(effect.planId, scope, dispatch)
            }
            else -> {
                // 其他Effect的处理
            }
        }
    }

    /**
     * 加载每日统计数据
     */
    private fun handleLoadDailyStats(
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                Timber.d("加载每日统计数据")

                // 使用 GetStatsUseCase 获取最近7天的数据
                getStatsUseCase(
                    timeRange = com.example.gymbro.domain.workout.model.stats.TimeRange.WEEK,
                ).collect { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            // 从 UnifiedWorkoutStatistics 中提取 DailyStats
                            val dailyStats = result.data.dailyStats
                            dispatch(PlanContract.Intent.DailyStatsLoaded(dailyStats.toImmutableList()))
                            Timber.d("成功加载每日统计数据: ${dailyStats.size} 条记录")
                        }
                        is ModernResult.Error -> {
                            Timber.e("加载每日统计数据失败: ${result.error}")
                            dispatch(
                                PlanContract.Intent.StatsLoadFailed(
                                    UiText.DynamicString("加载统计数据失败"),
                                ),
                            )
                        }
                        is ModernResult.Loading -> {
                            // 可以选择显示加载状态
                            Timber.d("正在加载每日统计数据...")
                        }
                    }
                }
            } catch (exception: Exception) {
                Timber.e(exception, "加载每日统计数据时发生异常")
                dispatch(
                    PlanContract.Intent.StatsLoadFailed(
                        UiText.DynamicString("加载统计数据时发生异常: ${exception.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 加载周统计数据
     */
    private fun handleLoadWeeklyStats(
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                Timber.d("加载周统计数据")

                // 使用 GetStatsUseCase 获取本周数据
                getStatsUseCase(
                    timeRange = com.example.gymbro.domain.workout.model.stats.TimeRange.WEEK,
                ).collect { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            // 从 UnifiedWorkoutStatistics 中构建 WeeklyStats
                            val weeklyStats = com.example.gymbro.domain.workout.model.calendar.WeeklyStats(
                                completed = result.data.totalSessions,
                                total = result.data.totalSessions + 3, // 假设总计划数
                                totalVolume = result.data.totalWeight.toFloat(),
                                avgRpe = result.data.avgRpe ?: 0f,
                                completionRate = if (result.data.totalSessions > 0) {
                                    result.data.totalSessions.toFloat() / (result.data.totalSessions + 3)
                                } else {
                                    0f
                                },
                            )
                            dispatch(PlanContract.Intent.WeeklyStatsLoaded(weeklyStats))
                            Timber.d("成功加载周统计数据")
                        }
                        is ModernResult.Error -> {
                            Timber.e("加载周统计数据失败: ${result.error}")
                            dispatch(
                                PlanContract.Intent.StatsLoadFailed(
                                    UiText.DynamicString("加载周统计数据失败"),
                                ),
                            )
                        }
                        is ModernResult.Loading -> {
                            Timber.d("正在加载周统计数据...")
                        }
                    }
                }
            } catch (exception: Exception) {
                Timber.e(exception, "加载周统计数据时发生异常")
                dispatch(
                    PlanContract.Intent.StatsLoadFailed(
                        UiText.DynamicString("加载周统计数据时发生异常: ${exception.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 加载指定计划的统计数据
     */
    private fun handleLoadStatsForPlan(
        planId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                Timber.d("加载计划统计数据: planId=$planId")

                // 使用 GetStatsUseCase 获取最近30天的数据，然后筛选指定计划
                getStatsUseCase(
                    timeRange = com.example.gymbro.domain.workout.model.stats.TimeRange.MONTH,
                ).collect { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            // 筛选指定计划的统计数据
                            val planStats = result.data.dailyStats.filter {
                                it.planId == planId
                            }
                            dispatch(PlanContract.Intent.DailyStatsLoaded(planStats.toImmutableList()))
                            Timber.d("成功加载计划统计数据: ${planStats.size} 条记录")
                        }
                        is ModernResult.Error -> {
                            Timber.e("加载计划统计数据失败: ${result.error}")
                            dispatch(
                                PlanContract.Intent.StatsLoadFailed(
                                    UiText.DynamicString("加载计划统计数据失败"),
                                ),
                            )
                        }
                        is ModernResult.Loading -> {
                            Timber.d("正在加载计划统计数据...")
                        }
                    }
                }
            } catch (exception: Exception) {
                Timber.e(exception, "加载计划统计数据时发生异常")
                dispatch(
                    PlanContract.Intent.StatsLoadFailed(
                        UiText.DynamicString("加载计划统计数据时发生异常: ${exception.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 加载指定日期范围的统计数据
     */
    private fun handleLoadStatsForDateRange(
        startDate: kotlinx.datetime.LocalDate,
        endDate: kotlinx.datetime.LocalDate,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                Timber.d("加载日期范围统计数据: $startDate - $endDate")

                // 使用 GetStatsUseCase 获取自定义日期范围的数据
                getStatsUseCase(
                    timeRange = com.example.gymbro.domain.workout.model.stats.TimeRange.CUSTOM,
                    startDate = startDate,
                    endDate = endDate,
                ).collect { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val dailyStats = result.data.dailyStats
                            dispatch(PlanContract.Intent.DailyStatsLoaded(dailyStats.toImmutableList()))
                            Timber.d("成功加载日期范围统计数据: ${dailyStats.size} 条记录")
                        }
                        is ModernResult.Error -> {
                            Timber.e("加载日期范围统计数据失败: ${result.error}")
                            dispatch(
                                PlanContract.Intent.StatsLoadFailed(
                                    UiText.DynamicString("加载日期范围统计数据失败"),
                                ),
                            )
                        }
                        is ModernResult.Loading -> {
                            Timber.d("正在加载日期范围统计数据...")
                        }
                    }
                }
            } catch (exception: Exception) {
                Timber.e(exception, "加载日期范围统计数据时发生异常")
                dispatch(
                    PlanContract.Intent.StatsLoadFailed(
                        UiText.DynamicString("加载日期范围统计数据时发生异常: ${exception.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 刷新统计数据
     */
    private fun handleRefreshStats(
        planId: String?,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                Timber.d("刷新统计数据: planId=$planId")

                if (planId != null) {
                    handleLoadStatsForPlan(planId, scope, dispatch)
                } else {
                    handleLoadDailyStats(scope, dispatch)
                }

                handleLoadWeeklyStats(scope, dispatch)
            } catch (exception: Exception) {
                Timber.e(exception, "刷新统计数据时发生异常")
                dispatch(
                    PlanContract.Intent.StatsLoadFailed(
                        UiText.DynamicString("刷新统计数据时发生异常: ${exception.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 加载每日统计数据
     */
    private fun loadDailyStats(
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // TODO: 集成 GetDailyStatsUseCase
                // 暂时使用模拟数据
                val mockStats = persistentListOf<DailyStats>()
                dispatch(PlanContract.Intent.DailyStatsLoaded(mockStats))
                Timber.d("成功加载每日统计数据")
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("加载每日统计数据失败")
                dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                Timber.e(exception, "加载每日统计数据时发生异常")
            }
        }
    }

    /**
     * 加载周统计数据
     */
    private fun loadWeeklyStats(
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // TODO: 集成 GetWeeklyStatsUseCase
                // 暂时使用模拟数据
                val mockWeeklyStats = WeeklyStats()
                dispatch(PlanContract.Intent.WeeklyStatsLoaded(mockWeeklyStats))
                Timber.d("成功加载周统计数据")
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("加载周统计数据失败")
                dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                Timber.e(exception, "加载周统计数据时发生异常")
            }
        }
    }

    /**
     * 加载指定计划的统计数据
     */
    private fun loadStatsForPlan(
        planId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // TODO: 集成 GetPlanStatsUseCase
                // 暂时使用模拟数据
                val mockStats = persistentListOf<DailyStats>()
                dispatch(PlanContract.Intent.DailyStatsLoaded(mockStats))
                Timber.d("成功加载计划统计数据: $planId")
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("加载计划统计数据失败")
                dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                Timber.e(exception, "加载计划统计数据时发生异常: $planId")
            }
        }
    }

    /**
     * 加载指定日期范围的统计数据
     */
    private fun loadStatsForDateRange(
        startDate: LocalDate,
        endDate: LocalDate,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // TODO: 集成 GetStatsForDateRangeUseCase
                // 暂时使用模拟数据
                val mockStats = persistentListOf<DailyStats>()
                dispatch(PlanContract.Intent.DailyStatsLoaded(mockStats))
                Timber.d("成功加载日期范围统计数据: $startDate - $endDate")
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("加载日期范围统计数据失败")
                dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                Timber.e(exception, "加载日期范围统计数据时发生异常: $startDate - $endDate")
            }
        }
    }

    /**
     * 获取指定日期的进度
     */
    private fun getProgressForDate(
        date: LocalDate,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // TODO: 集成 GetProgressForDateUseCase
                // 暂时使用模拟数据
                Timber.d("获取日期进度: $date")
                // 结果将通过 DailyStatsLoaded 返回
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("获取日期进度失败")
                dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                Timber.e(exception, "获取日期进度时发生异常: $date")
            }
        }
    }

    /**
     * 获取指定周的进度
     */
    private fun getWeekProgress(
        weekNumber: Int,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // TODO: 集成 GetWeekProgressUseCase
                // 暂时使用模拟数据
                Timber.d("获取周进度: $weekNumber")
                // 结果将通过 WeeklyStatsLoaded 返回
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("获取周进度失败")
                dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                Timber.e(exception, "获取周进度时发生异常: $weekNumber")
            }
        }
    }

    /**
     * 切换某天的完成状态
     */
    private fun toggleDayCompleted(
        date: LocalDate,
        planId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                // TODO: 集成 ToggleDayCompletedUseCase
                // 暂时使用模拟逻辑
                val isCompleted = true // 模拟切换结果
                val updatedStats: DailyStats? = null // 模拟更新后的统计数据

                dispatch(PlanContract.Intent.ProgressUpdatedResult(date, isCompleted, updatedStats))
                Timber.d("成功切换日期完成状态: $date, 计划: $planId")
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("切换完成状态失败")
                dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                Timber.e(exception, "切换日期完成状态时发生异常: $date, 计划: $planId")
            }
        }
    }

    // ==================== 日历集成增强功能处理方法 ====================

    /**
     * 处理导出 Plan 到日历
     */
    private fun handleExportPlanToCalendar(
        planId: String,
        startDate: LocalDate,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                Timber.d("开始导出 Plan 到日历: planId=$planId, startDate=$startDate")

                when (
                    val result = planToCalendarUseCase.convertPlanToCalendarEntries(
                        planId,
                        startDate,
                    )
                ) {
                    is ModernResult.Success -> {
                        val entries = result.data
                        Timber.d("成功导出 ${entries.size} 个日历条目")

                        // 发送成功的结果 Intent
                        dispatch(PlanContract.Intent.CalendarEntriesGenerated(entries))
                    }
                    is ModernResult.Error -> {
                        val errorMessage = UiText.DynamicString("导出到日历失败: ${result.error.message}")
                        dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                        Timber.e(result.error, "导出 Plan 到日历失败")
                    }
                    is ModernResult.Loading -> {
                        // 不应该发生，因为这是 suspend 函数
                    }
                }
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("导出到日历时发生异常")
                dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                Timber.e(exception, "导出 Plan 到日历时发生异常")
            }
        }
    }

    /**
     * 处理获取 Plan 日历摘要
     */
    private fun handleGetPlanCalendarSummary(
        planId: String,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                Timber.d("开始获取 Plan 日历摘要: planId=$planId")

                when (val result = planToCalendarUseCase.getPlanCalendarSummary(planId)) {
                    is ModernResult.Success -> {
                        val summary = result.data
                        Timber.d("成功获取 Plan 日历摘要: ${summary.planName}")

                        // 发送成功的结果 Intent
                        dispatch(PlanContract.Intent.CalendarSummaryGenerated(summary))
                    }
                    is ModernResult.Error -> {
                        val errorMessage = UiText.DynamicString("获取日历摘要失败: ${result.error.message}")
                        dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                        Timber.e(result.error, "获取 Plan 日历摘要失败")
                    }
                    is ModernResult.Loading -> {
                        // 不应该发生，因为这是 suspend 函数
                    }
                }
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("获取日历摘要时发生异常")
                dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                Timber.e(exception, "获取 Plan 日历摘要时发生异常")
            }
        }
    }

    /**
     * 处理批量导入多个 Plan 到日历
     */
    private fun handleImportMultiplePlansToCalendar(
        planIds: List<String>,
        startDates: List<LocalDate>,
        scope: CoroutineScope,
        dispatch: (PlanContract.Intent) -> Unit,
    ) {
        scope.launch(ioDispatcher) {
            try {
                Timber.d("开始批量导入 ${planIds.size} 个 Plan 到日历")

                when (
                    val result = planToCalendarUseCase.importMultiplePlansToCalendar(
                        planIds,
                        startDates,
                    )
                ) {
                    is ModernResult.Success -> {
                        val entries = result.data
                        val successCount = entries.distinctBy { it.planId }.size
                        val totalCount = planIds.size

                        Timber.d("成功导入 $successCount/$totalCount 个 Plan，共 ${entries.size} 个日历条目")

                        // 发送成功的结果 Intent
                        dispatch(
                            PlanContract.Intent.MultipleCalendarEntriesGenerated(
                                entries,
                                successCount,
                                totalCount,
                            ),
                        )
                    }
                    is ModernResult.Error -> {
                        val errorMessage = UiText.DynamicString("批量导入到日历失败: ${result.error.message}")
                        dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                        Timber.e(result.error, "批量导入 Plan 到日历失败")
                    }
                    is ModernResult.Loading -> {
                        // 不应该发生，因为这是 suspend 函数
                    }
                }
            } catch (exception: Exception) {
                val errorMessage = UiText.DynamicString("批量导入到日历时发生异常")
                dispatch(PlanContract.Intent.StatsLoadFailed(errorMessage))
                Timber.e(exception, "批量导入 Plan 到日历时发生异常")
            }
        }
    }
}
