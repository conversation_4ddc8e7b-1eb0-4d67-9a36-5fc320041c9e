package com.example.gymbro.features.workout.json.wrapper

import com.example.gymbro.features.workout.json.core.JsonConstants
import com.example.gymbro.features.workout.json.core.ProcessingMode
import com.example.gymbro.features.workout.json.processor.TemplateJsonProcessor
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber

/**
 * P4: 统一 JSON 包装器 - 增强版本
 *
 * P4 改进：
 * 1. 优化模板序列化逻辑，确保customSets数据完整写回JSON
 * 2. 添加JSON写回的完整性验证
 * 3. 优化JSON格式，提高可读性和兼容性
 * 4. 数据一致性保障机制
 *
 * Template 和 Session 共用的统一包装器，支持可扩展数据字段和动态处理
 * 这是整个 JSON 处理系统的核心组件
 *
 * <AUTHOR> AI Assistant
 * @since 1.0.0
 */
class UnifiedJsonWrapper(
    private val mode: ProcessingMode = ProcessingMode.TEMPLATE,
) {
    // P4: 优化JSON配置，提高可读性和兼容性
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        isLenient = true
        prettyPrint = true // P4: 启用格式化，提高可读性
        explicitNulls = true // P4: 确保null值被明确编码
        coerceInputValues = true // P4: 强制输入值转换，提高解析健壮性
    }

    /**
     * 包装完整的数据为 JSON
     */
    fun wrapData(data: Map<String, Any>): String {
        return when (mode) {
            ProcessingMode.TEMPLATE -> wrapTemplateData(data)
            ProcessingMode.SESSION -> wrapSessionData(data)
            ProcessingMode.UNIFIED -> wrapUnifiedData(data)
            else -> wrapTemplateData(data)
        }
    }

    /**
     * P4: 包装 WorkoutTemplateDto 为 JSON - 增强版本
     * 确保customSets数据完整写回JSON，添加完整性验证
     */
    fun wrapTemplate(template: WorkoutTemplateDto): String {
        return try {
            // P4: CRITICAL日志 - 追踪JSON包装过程
            Timber.tag("CRITICAL-JSON").i("🔥 [P4-JSON-WRAP] 开始包装模板: ${template.name}")
            Timber.tag(
                "CRITICAL-JSON",
            ).i("🔥 [P4-JSON-WRAP] 模板ID: ${template.id}, 动作数: ${template.exercises.size}")

            // P4: 验证customSets数据完整性
            template.exercises.forEachIndexed { exerciseIndex, exercise ->
                Timber.tag(
                    "CRITICAL-JSON",
                ).i(
                    "🔥 [P4-JSON-WRAP] 动作${exerciseIndex + 1}: ${exercise.exerciseName}, customSets=${exercise.customSets.size}",
                )
                exercise.customSets.forEachIndexed { setIndex, set ->
                    Timber.tag(
                        "CRITICAL-JSON",
                    ).i(
                        "🔥 [P4-JSON-WRAP] 组${setIndex + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s",
                    )
                }
            }

            val jsonResult = with(TemplateJsonProcessor) {
                template.toJson()
            }

            // P4: JSON写回完整性验证
            val validationResult = validateTemplateJson(jsonResult, template)
            if (!validationResult.isValid) {
                Timber.tag("CRITICAL-JSON").e("🔥 [P4-JSON-WRAP] JSON验证失败: ${validationResult.errors}")
                throw IllegalStateException("JSON验证失败: ${validationResult.errors.joinToString(", ")}")
            }

            Timber.tag("CRITICAL-JSON").i("🔥 [P4-JSON-WRAP] JSON包装成功，长度: ${jsonResult.length}")
            jsonResult
        } catch (e: Exception) {
            Timber.tag("CRITICAL-JSON").e(e, "🔥 [P4-JSON-WRAP] 模板包装失败: ${template.name}")
            createFallbackTemplateJson(template.id, template.name)
        }
    }

    /**
     * 扩展数据字段
     */
    fun extendFields(originalJson: String, extensionFields: Map<String, Any>): String {
        return try {
            val originalData = json.parseToJsonElement(originalJson)
            val extendedData = mergeJsonData(originalData, extensionFields)
            json.encodeToString(extendedData)
        } catch (e: Exception) {
            Timber.e(e, "字段扩展失败")
            originalJson
        }
    }

    /**
     * 动态修改字段
     */
    fun modifyField(originalJson: String, fieldName: String, value: Any): String {
        return try {
            when (mode) {
                ProcessingMode.TEMPLATE -> modifyTemplateField(originalJson, fieldName, value)
                ProcessingMode.SESSION -> modifySessionField(originalJson, fieldName, value)
                else -> modifyTemplateField(originalJson, fieldName, value)
            }
        } catch (e: Exception) {
            Timber.e(e, "字段修改失败: $fieldName")
            originalJson
        }
    }

    /**
     * 批量修改字段
     */
    fun batchModifyFields(originalJson: String, modifications: Map<String, Any>): String {
        var result = originalJson
        modifications.forEach { (fieldName, value) ->
            result = modifyField(result, fieldName, value)
        }
        return result
    }

    /**
     * 获取字段值
     */
    fun getFieldValue(jsonString: String, fieldName: String): Any? {
        return try {
            val jsonElement = json.parseToJsonElement(jsonString)
            extractFieldValue(jsonElement, fieldName)
        } catch (e: Exception) {
            Timber.e(e, "获取字段值失败: $fieldName")
            null
        }
    }

    /**
     * P4: 验证数据完整性 - 增强版本
     */
    fun validateData(jsonString: String): ValidationResult {
        return try {
            when (mode) {
                ProcessingMode.TEMPLATE -> validateTemplateData(jsonString)
                ProcessingMode.SESSION -> validateSessionData(jsonString)
                else -> validateTemplateData(jsonString)
            }
        } catch (e: Exception) {
            Timber.tag("CRITICAL-JSON").e(e, "🔥 [P4-JSON-VALIDATE] 数据验证失败")
            ValidationResult(false, listOf("数据验证异常: ${e.message}"))
        }
    }

    /**
     * P4: 验证模板JSON与原始数据的一致性
     */
    private fun validateTemplateJson(
        jsonString: String,
        originalTemplate: WorkoutTemplateDto,
    ): ValidationResult {
        return try {
            Timber.tag("CRITICAL-JSON").i("🔥 [P4-JSON-VALIDATE] 开始验证模板JSON一致性")

            // 基础JSON格式验证
            val jsonElement = json.parseToJsonElement(jsonString)

            // 验证必需字段存在
            val requiredFields = listOf("id", "name", "exercises")
            val missingFields = mutableListOf<String>()

            requiredFields.forEach { field ->
                if (!jsonString.contains("\"$field\"")) {
                    missingFields.add(field)
                }
            }

            if (missingFields.isNotEmpty()) {
                return ValidationResult(false, listOf("缺少必需字段: ${missingFields.joinToString(", ")}"))
            }

            // 验证动作数量一致性
            val exerciseCountInJson = jsonString.split("\"exerciseName\"").size - 1
            if (exerciseCountInJson != originalTemplate.exercises.size) {
                return ValidationResult(
                    false,
                    listOf("动作数量不一致: JSON中$exerciseCountInJson vs 原始${originalTemplate.exercises.size}"),
                )
            }

            // 验证customSets数据完整性
            originalTemplate.exercises.forEachIndexed { index, exercise ->
                if (exercise.customSets.isNotEmpty()) {
                    val exerciseName = exercise.exerciseName
                    if (!jsonString.contains(exerciseName)) {
                        return ValidationResult(false, listOf("动作 $exerciseName 在JSON中缺失"))
                    }

                    // 验证customSets数据是否包含在JSON中
                    val hasCustomSetsData = exercise.customSets.any { set ->
                        jsonString.contains("\"targetWeight\":${set.targetWeight}") ||
                            jsonString.contains("\"targetReps\":${set.targetReps}")
                    }

                    if (!hasCustomSetsData) {
                        Timber.tag(
                            "CRITICAL-JSON",
                        ).w("🔥 [P4-JSON-VALIDATE] 动作 $exerciseName 的customSets数据可能缺失")
                    }
                }
            }

            Timber.tag("CRITICAL-JSON").i("🔥 [P4-JSON-VALIDATE] JSON验证通过")
            ValidationResult(true)
        } catch (e: Exception) {
            Timber.tag("CRITICAL-JSON").e(e, "🔥 [P4-JSON-VALIDATE] JSON验证异常")
            ValidationResult(false, listOf("JSON验证异常: ${e.message}"))
        }
    }

    /**
     * 实时更新数据
     */
    fun updateRealtime(originalJson: String, updates: Map<String, Any>): String {
        return try {
            // 实时更新不进行复杂验证，直接应用更改
            batchModifyFields(originalJson, updates)
        } catch (e: Exception) {
            Timber.e(e, "实时更新失败")
            originalJson
        }
    }

    // ==================== 私有方法 ====================

    private fun wrapTemplateData(data: Map<String, Any>): String {
        val templateData = mutableMapOf<String, Any>()

        // 基础字段
        templateData[JsonConstants.TemplateFields.NAME] = data[JsonConstants.TemplateFields.NAME] ?: JsonConstants.Defaults.DEFAULT_NAME
        templateData[JsonConstants.TemplateFields.DESCRIPTION] = data[JsonConstants.TemplateFields.DESCRIPTION] ?: JsonConstants.Defaults.DEFAULT_DESCRIPTION
        templateData[JsonConstants.TemplateFields.GLOBAL_REST_TIME] = data[JsonConstants.TemplateFields.GLOBAL_REST_TIME] ?: JsonConstants.Defaults.DEFAULT_GLOBAL_REST_TIME

        // 可扩展字段
        JsonConstants.TemplateFields::class.java.declaredFields.forEach { field ->
            val fieldName = field.get(null) as? String
            if (fieldName != null && data.containsKey(fieldName)) {
                templateData[fieldName] = data[fieldName]!!
            }
        }

        return try {
            json.encodeToString(templateData)
        } catch (e: Exception) {
            Timber.e(e, "Template 数据包装失败")
            "{}"
        }
    }

    private fun wrapSessionData(data: Map<String, Any>): String {
        val sessionData = mutableMapOf<String, Any>()

        // 继承 Template 字段
        sessionData.putAll(
            data.filterKeys { key ->
                listOf(
                    JsonConstants.TemplateFields.NAME,
                    JsonConstants.TemplateFields.DESCRIPTION,
                    JsonConstants.TemplateFields.SETS,
                    JsonConstants.TemplateFields.REPS,
                    JsonConstants.TemplateFields.WEIGHT,
                    JsonConstants.TemplateFields.GLOBAL_REST_TIME,
                ).contains(key)
            },
        )

        // Session 特有字段
        JsonConstants.SessionFields::class.java.declaredFields.forEach { field ->
            val fieldName = field.get(null) as? String
            if (fieldName != null && data.containsKey(fieldName)) {
                sessionData[fieldName] = data[fieldName]!!
            }
        }

        return try {
            json.encodeToString(sessionData)
        } catch (e: Exception) {
            Timber.e(e, "Session 数据包装失败")
            "{}"
        }
    }

    private fun wrapUnifiedData(data: Map<String, Any>): String {
        // 统一模式：同时支持 Template 和 Session 字段
        return try {
            json.encodeToString(data)
        } catch (e: Exception) {
            Timber.e(e, "统一数据包装失败")
            "{}"
        }
    }

    private fun modifyTemplateField(originalJson: String, fieldName: String, value: Any): String {
        val template = TemplateJsonProcessor.fromJson(originalJson) ?: return originalJson

        val modifiedTemplate = when (fieldName) {
            JsonConstants.TemplateFields.NAME -> template.copy(name = value.toString())
            JsonConstants.TemplateFields.DESCRIPTION -> template.copy(description = value.toString())
            else -> template
        }

        return with(TemplateJsonProcessor) { modifiedTemplate.toJson() }
    }

    private fun modifySessionField(originalJson: String, fieldName: String, value: Any): String {
        val jsonElement = json.parseToJsonElement(originalJson)
        val mutableMap = jsonElement.toString().let { json.decodeFromString<MutableMap<String, Any>>(it) }

        when (fieldName) {
            JsonConstants.SessionFields.SESSION_STATUS,
            JsonConstants.SessionFields.CURRENT_EXERCISE_INDEX,
            JsonConstants.SessionFields.CURRENT_SET_INDEX,
            -> {
                mutableMap[fieldName] = value
            }
            JsonConstants.SessionFields.ELAPSED_TIME,
            JsonConstants.SessionFields.TOTAL_DURATION,
            -> {
                mutableMap[fieldName] = (value as? Number)?.toLong() ?: 0L
            }
        }

        return json.encodeToString(mutableMap)
    }

    private fun validateTemplateData(jsonString: String): ValidationResult {
        val template = TemplateJsonProcessor.fromJson(jsonString)
        return if (template != null) {
            ValidationResult(true, emptyList())
        } else {
            ValidationResult(false, listOf("Template 数据格式无效"))
        }
    }

    private fun validateSessionData(jsonString: String): ValidationResult {
        return try {
            val jsonElement = json.parseToJsonElement(jsonString)
            ValidationResult(true, emptyList())
        } catch (e: Exception) {
            ValidationResult(false, listOf("Session 数据格式无效"))
        }
    }

    private fun extractFieldValue(jsonElement: kotlinx.serialization.json.JsonElement, fieldName: String): Any? {
        return when (jsonElement) {
            is kotlinx.serialization.json.JsonObject -> {
                jsonElement[fieldName]?.let { element ->
                    when (element) {
                        is kotlinx.serialization.json.JsonPrimitive -> {
                            when {
                                element.isString -> element.content
                                else -> element.content.toDoubleOrNull() ?: element.content
                            }
                        }
                        else -> element.toString()
                    }
                }
            }
            else -> null
        }
    }

    private fun mergeJsonData(
        original: kotlinx.serialization.json.JsonElement,
        extensions: Map<String, Any>,
    ): kotlinx.serialization.json.JsonElement {
        // 简化实现：直接合并到原始数据中
        val originalMap = json.decodeFromString<MutableMap<String, Any>>(original.toString())
        originalMap.putAll(extensions)
        return json.parseToJsonElement(json.encodeToString(originalMap))
    }

    private fun createFallbackTemplateJson(id: String, name: String): String {
        return """{"id":"$id","name":"$name","exercises":[],"description":"","globalRestTime":90}"""
    }

    // ==================== 阶段 4 新增功能：动态字段修改和实时更新 ====================

    /**
     * 可扩展数据字段定义
     */
    enum class ExtensibleField {
        NAME, // 名称
        DESCRIPTION, // 描述
        SETS, // 组数
        REPS, // 次数
        WEIGHT, // 重量
        GLOBAL_REST_TIME, // 全局倒计时时间
        NOTES, // 备注
        CUSTOM_FIELD, // 自定义字段
    }

    /**
     * 动态字段修改数据类
     */
    data class FieldUpdate(
        val field: ExtensibleField,
        val value: Any,
        val targetId: String? = null, // 用于指定特定的动作或组
        val customFieldName: String? = null, // 用于自定义字段
    )

    /**
     * 实时更新配置
     */
    data class RealTimeUpdateConfig(
        // 🔥 已移除：enableAutoSave - 自动保存功能已被移除
        val saveIntervalMs: Long = 1000L,
        val enableValidation: Boolean = true,
        val enableSecurityCheck: Boolean = true,
    )

    /**
     * 动态修改 JSON 数据中的字段
     */
    fun updateField(jsonString: String, update: FieldUpdate): String {
        return try {
            Timber.d("动态修改字段: ${update.field} = ${update.value}")

            when (mode) {
                ProcessingMode.TEMPLATE -> updateTemplateField(jsonString, update)
                ProcessingMode.SESSION -> updateSessionField(jsonString, update)
                ProcessingMode.UNIFIED -> updateUnifiedField(jsonString, update)
                else -> updateTemplateField(jsonString, update)
            }
        } catch (e: Exception) {
            Timber.e(e, "动态字段修改失败: ${update.field}")
            jsonString // 返回原始数据
        }
    }

    /**
     * 批量更新多个字段
     */
    fun batchUpdateFields(jsonString: String, updates: List<FieldUpdate>): String {
        return try {
            var currentJson = jsonString

            updates.forEach { update ->
                currentJson = updateField(currentJson, update)
            }

            currentJson
        } catch (e: Exception) {
            Timber.e(e, "批量字段更新失败: ${updates.size} 个更新")
            jsonString
        }
    }

    /**
     * 实时更新机制
     */
    fun enableRealTimeUpdate(
        jsonString: String,
        config: RealTimeUpdateConfig,
        onUpdate: (String) -> Unit,
    ): String {
        return try {
            // 验证数据
            if (config.enableValidation) {
                val validationResult = validateJsonData(jsonString)
                if (!validationResult.isValid) {
                    Timber.w("实时更新数据验证失败: ${validationResult.errors}")
                }
            }

            // 安全检查
            if (config.enableSecurityCheck) {
                if (!performSecurityCheck(jsonString)) {
                    Timber.w("实时更新安全检查失败")
                    return jsonString
                }
            }

            // 触发更新回调
            onUpdate(jsonString)

            jsonString
        } catch (e: Exception) {
            Timber.e(e, "实时更新机制失败")
            jsonString
        }
    }

    /**
     * 更新 Template 字段
     */
    private fun updateTemplateField(jsonString: String, update: FieldUpdate): String {
        return try {
            val template = json.decodeFromString<WorkoutTemplateDto>(jsonString)

            val updatedTemplate = when (update.field) {
                ExtensibleField.NAME -> template.copy(name = update.value.toString())
                ExtensibleField.DESCRIPTION -> template.copy(description = update.value.toString())
                ExtensibleField.GLOBAL_REST_TIME -> {
                    // 更新所有动作的休息时间
                    val restTime = (update.value as? Number)?.toInt() ?: JsonConstants.DEFAULT_REST_TIME_SECONDS
                    template.copy(
                        exercises = template.exercises.map { exercise ->
                            exercise.copy(restTimeSeconds = restTime)
                        },
                    )
                }
                ExtensibleField.SETS, ExtensibleField.REPS, ExtensibleField.WEIGHT -> {
                    updateTemplateExerciseField(template, update)
                }
                ExtensibleField.NOTES -> {
                    updateTemplateNotesField(template, update)
                }
                ExtensibleField.CUSTOM_FIELD -> {
                    updateTemplateCustomField(template, update)
                }
            }

            json.encodeToString(updatedTemplate)
        } catch (e: Exception) {
            Timber.e(e, "Template 字段更新失败: ${update.field}")
            jsonString
        }
    }

    /**
     * 更新 Session 字段
     */
    private fun updateSessionField(jsonString: String, update: FieldUpdate): String {
        return try {
            // Session 模式的字段更新逻辑
            // 这里需要根据实际的 Session 数据结构来实现
            Timber.d("Session 字段更新: ${update.field}")

            // 暂时返回原始数据，等待 Session 数据结构确定后实现
            jsonString
        } catch (e: Exception) {
            Timber.e(e, "Session 字段更新失败: ${update.field}")
            jsonString
        }
    }

    /**
     * 更新统一模式字段
     */
    private fun updateUnifiedField(jsonString: String, update: FieldUpdate): String {
        return try {
            // 统一模式的字段更新逻辑
            // 根据数据类型自动选择 Template 或 Session 模式
            if (jsonString.contains("\"exercises\"")) {
                updateTemplateField(jsonString, update)
            } else {
                updateSessionField(jsonString, update)
            }
        } catch (e: Exception) {
            Timber.e(e, "统一模式字段更新失败: ${update.field}")
            jsonString
        }
    }

    /**
     * 更新模板动作字段
     */
    private fun updateTemplateExerciseField(
        template: WorkoutTemplateDto,
        update: FieldUpdate,
    ): WorkoutTemplateDto {
        return template.copy(
            exercises = template.exercises.map { exercise ->
                if (update.targetId == null || exercise.id == update.targetId) {
                    when (update.field) {
                        ExtensibleField.SETS -> exercise.copy(
                            sets = (update.value as? Number)?.toInt() ?: exercise.sets,
                        )
                        ExtensibleField.REPS -> exercise.copy(
                            reps = (update.value as? Number)?.toInt() ?: exercise.reps,
                        )
                        ExtensibleField.WEIGHT -> exercise.copy(
                            targetWeight = (update.value as? Number)?.toFloat(),
                        )
                        else -> exercise
                    }
                } else {
                    exercise
                }
            },
        )
    }

    /**
     * 更新模板备注字段
     */
    private fun updateTemplateNotesField(
        template: WorkoutTemplateDto,
        update: FieldUpdate,
    ): WorkoutTemplateDto {
        return template.copy(
            exercises = template.exercises.map { exercise ->
                if (update.targetId == null || exercise.id == update.targetId) {
                    exercise.copy(notes = update.value.toString())
                } else {
                    exercise
                }
            },
        )
    }

    /**
     * 更新模板自定义字段
     */
    private fun updateTemplateCustomField(
        template: WorkoutTemplateDto,
        update: FieldUpdate,
    ): WorkoutTemplateDto {
        // 自定义字段可以通过扩展数据的方式实现
        // 这里提供基础框架，具体实现可以根据需求扩展
        Timber.d("更新自定义字段: ${update.customFieldName} = ${update.value}")
        return template
    }

    /**
     * 验证 JSON 数据
     */
    private fun validateJsonData(jsonString: String): ValidationResult {
        return try {
            // 基础 JSON 格式验证
            json.parseToJsonElement(jsonString)

            // 数据完整性验证
            val hasRequiredFields = when (mode) {
                ProcessingMode.TEMPLATE -> jsonString.contains("\"id\"") && jsonString.contains("\"name\"")
                ProcessingMode.SESSION -> jsonString.contains("\"exerciseId\"")
                else -> jsonString.contains("\"id\"")
            }

            if (!hasRequiredFields) {
                return ValidationResult(false, listOf("缺少必需字段"))
            }

            ValidationResult(true)
        } catch (e: Exception) {
            ValidationResult(false, listOf("JSON 格式错误: ${e.message}"))
        }
    }

    /**
     * 执行安全检查
     */
    private fun performSecurityCheck(jsonString: String): Boolean {
        return try {
            // 检查数据大小
            if (jsonString.length > JsonConstants.MAX_JSON_SIZE) {
                Timber.w("JSON 数据过大: ${jsonString.length}")
                return false
            }

            // 检查恶意内容
            val maliciousPatterns = listOf("<script", "javascript:", "eval(", "function(")
            if (maliciousPatterns.any { jsonString.contains(it, ignoreCase = true) }) {
                Timber.w("检测到潜在恶意内容")
                return false
            }

            true
        } catch (e: Exception) {
            Timber.e(e, "安全检查失败")
            false
        }
    }
}

/**
 * 验证结果
 */
data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String> = emptyList(),
)
