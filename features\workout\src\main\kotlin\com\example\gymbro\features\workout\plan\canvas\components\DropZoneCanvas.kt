package com.example.gymbro.features.workout.plan.canvas.components

import androidx.compose.animation.core.*
import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.features.workout.plan.canvas.coordinator.UnifiedDragCoordinator
import com.example.gymbro.features.workout.plan.canvas.model.*

/**
 * Drop Zone 画布组件 - 统一的拖拽目标区域
 * 
 * 🎯 核心功能：
 * - 4周x7天的网格布局画布
 * - 智能Drop Zone高亮和动画
 * - 已安排项目的可视化展示
 * - 支持撤销/重做的操作历史
 * - M3设计规范的视觉反馈
 * 
 * 🏗️ 设计特点：
 * - 响应式布局：自适应不同屏幕尺寸
 * - 智能对齐：网格对齐和自由拖放支持
 * - 状态管理：完整的拖拽状态可视化
 * - 性能优化：LazyColumn虚拟化长列表
 */
@Composable
fun DropZoneCanvas(
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    onCanvasUpdated: (PlanCanvasData) -> Unit,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    // 监听拖拽状态
    val dragState by coordinator.dragState.collectAsState()
    val dropZones by coordinator.dropZones.collectAsState()
    
    // 画布配置
    val config = canvasData.canvasConfig
    
    Surface(
        modifier = modifier.fillMaxSize(),
        color = Tokens.Color.Gray100,
        shape = RoundedCornerShape(Tokens.Radius.Large)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium)
        ) {
            // 画布头部
            CanvasHeader(
                canvasData = canvasData,
                coordinator = coordinator,
                onUndo = { coordinator.undo() },
                onRedo = { coordinator.redo() }
            )
            
            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))
            
            // 画布主体
            when (config.layoutMode) {
                CanvasLayoutMode.WEEKLY_GRID -> {
                    WeeklyGridCanvas(
                        canvasData = canvasData,
                        coordinator = coordinator,
                        dragState = dragState,
                        dropZones = dropZones,
                        density = density
                    )
                }
                CanvasLayoutMode.TIMELINE -> {
                    TimelineCanvas(
                        canvasData = canvasData,
                        coordinator = coordinator,
                        dragState = dragState,
                        dropZones = dropZones
                    )
                }
                CanvasLayoutMode.FREE_CANVAS -> {
                    FreeCanvas(
                        canvasData = canvasData,
                        coordinator = coordinator,
                        dragState = dragState,
                        dropZones = dropZones
                    )
                }
            }
        }
    }
}

/**
 * 画布头部组件
 */
@Composable
private fun CanvasHeader(
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    onUndo: () -> Unit,
    onRedo: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧：计划信息
        Column {
            Text(
                text = canvasData.planName,
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.workoutColors.textPrimary,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "已安排 ${canvasData.getTotalScheduledItems()} 个项目",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.workoutColors.textSecondary
            )
        }
        
        // 右侧：操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
        ) {
            // 撤销按钮
            IconButton(
                onClick = onUndo,
                enabled = canvasData.canUndo()
            ) {
                Icon(
                    imageVector = Icons.Default.Add, // 替换为撤销图标
                    contentDescription = "撤销",
                    tint = if (canvasData.canUndo()) {
                        MaterialTheme.workoutColors.accentPrimary
                    } else {
                        MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.5f)
                    }
                )
            }
            
            // 重做按钮
            IconButton(
                onClick = onRedo,
                enabled = canvasData.canRedo()
            ) {
                Icon(
                    imageVector = Icons.Default.Add, // 替换为重做图标
                    contentDescription = "重做",
                    tint = if (canvasData.canRedo()) {
                        MaterialTheme.workoutColors.accentPrimary
                    } else {
                        MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.5f)
                    }
                )
            }
        }
    }
}

/**
 * 周网格画布
 */
@Composable
private fun WeeklyGridCanvas(
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    dragState: UnifiedDragCoordinator.DragState,
    dropZones: List<UnifiedDragCoordinator.DropZone>,
    density: androidx.compose.ui.unit.Density,
    modifier: Modifier = Modifier
) {
    val config = canvasData.canvasConfig
    
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        contentPadding = PaddingValues(vertical = Tokens.Spacing.Small)
    ) {
        items(config.weekCount) { weekIndex ->
            val week = weekIndex + 1
            
            WeekRow(
                week = week,
                canvasData = canvasData,
                coordinator = coordinator,
                dragState = dragState,
                dropZones = dropZones,
                density = density
            )
        }
    }
}

/**
 * 周行组件
 */
@Composable
private fun WeekRow(
    week: Int,
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    dragState: UnifiedDragCoordinator.DragState,
    dropZones: List<UnifiedDragCoordinator.DropZone>,
    density: androidx.compose.ui.unit.Density,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 周标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "第 $week 周",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.workoutColors.textPrimary,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = getWeekSummary(week, canvasData),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.workoutColors.textSecondary
            )
        }
        
        Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
        
        // 天网格
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            contentPadding = PaddingValues(horizontal = Tokens.Spacing.XSmall)
        ) {
            items(7) { dayIndex ->
                val day = dayIndex + 1
                val position = CanvasPosition(week = week, day = day, order = 0)
                val dayItems = canvasData.getItemsForDate(position.getDayIndex())
                
                DayDropZone(
                    week = week,
                    day = day,
                    items = dayItems,
                    coordinator = coordinator,
                    dragState = dragState,
                    dropZones = dropZones,
                    density = density
                )
            }
        }
    }
}

/**
 * 日期拖拽区域组件
 */
@Composable
private fun DayDropZone(
    week: Int,
    day: Int,
    items: List<CanvasItem>,
    coordinator: UnifiedDragCoordinator,
    dragState: UnifiedDragCoordinator.DragState,
    dropZones: List<UnifiedDragCoordinator.DropZone>,
    density: androidx.compose.ui.unit.Density,
    modifier: Modifier = Modifier
) {
    val zoneId = "dropzone_${week}_${day}"
    val dropZone = dropZones.find { it.id == zoneId }
    val isDragTarget = dragState is UnifiedDragCoordinator.DragState.Dragging &&
                      dropZone?.isActive == true
    
    // 动画效果
    val borderAlpha by animateFloatAsState(
        targetValue = if (isDragTarget) 1f else 0.3f,
        animationSpec = tween(300),
        label = "border_alpha"
    )
    
    val backgroundColor by animateColorAsState(
        targetValue = if (isDragTarget) {
            Tokens.Color.CTAPrimary.copy(alpha = 0.1f)
        } else {
            Tokens.Color.Gray200
        },
        animationSpec = tween(300),
        label = "background_color"
    )
    
    val scale by animateFloatAsState(
        targetValue = if (isDragTarget) 1.02f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "scale"
    )
    
    Box(
        modifier = modifier
            .width(120.dp)
            .height(160.dp)
            .scale(scale)
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(Tokens.Radius.Card)
            )
            .border(
                width = 2.dp,
                color = MaterialTheme.workoutColors.accentPrimary.copy(alpha = borderAlpha),
                shape = RoundedCornerShape(Tokens.Radius.Card)
            )
            .onGloballyPositioned { coordinates ->
                // 更新drop zone边界信息
                val bounds = Rect(
                    offset = coordinates.positionInRoot(),
                    size = Size(coordinates.size.width.toFloat(), coordinates.size.height.toFloat())
                )
                coordinator.updateDropZoneBounds(zoneId, bounds)
            }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Small),
            verticalArrangement = if (items.isEmpty()) Arrangement.Center else Arrangement.Top
        ) {
            // 日期标题
            Text(
                text = getDayName(day),
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.workoutColors.textSecondary,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
            
            if (items.isEmpty()) {
                // 空状态
                EmptyDropZoneContent(isDragTarget = isDragTarget)
            } else {
                // 已安排的项目
                Spacer(modifier = Modifier.height(Tokens.Spacing.XSmall))
                
                items.forEach { item ->
                    ScheduledItemCard(
                        item = item,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    if (item != items.last()) {
                        Spacer(modifier = Modifier.height(Tokens.Spacing.XSmall))
                    }
                }
            }
        }
    }
}

/**
 * 空拖拽区域内容
 */
@Composable
private fun EmptyDropZoneContent(
    isDragTarget: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Add,
            contentDescription = null,
            tint = if (isDragTarget) {
                MaterialTheme.workoutColors.accentPrimary
            } else {
                MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.5f)
            },
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.height(Tokens.Spacing.XSmall))
        
        Text(
            text = if (isDragTarget) "放置在这里" else "拖拽到这里",
            style = MaterialTheme.typography.labelSmall,
            color = if (isDragTarget) {
                MaterialTheme.workoutColors.accentPrimary
            } else {
                MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.5f)
            },
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 已安排项目卡片
 */
@Composable
private fun ScheduledItemCard(
    item: CanvasItem,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(Tokens.Radius.Small),
        color = when (item) {
            is CanvasItem.TemplateItem -> MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f)
            is CanvasItem.DraftItem -> MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.1f)
            is CanvasItem.CustomItem -> MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.1f)
        },
        shadowElevation = Tokens.Elevation.Small
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.XSmall)
        ) {
            Text(
                text = item.name,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.workoutColors.textPrimary,
                fontWeight = FontWeight.Medium,
                maxLines = 1
            )
            
            when (item) {
                is CanvasItem.TemplateItem -> {
                    Text(
                        text = "${item.exerciseCount}个动作",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.workoutColors.textSecondary
                    )
                }
                is CanvasItem.DraftItem -> {
                    Text(
                        text = "${item.exerciseCount}个动作${if (item.canPromote) " • 可转正" else ""}",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.workoutColors.textSecondary
                    )
                }
                is CanvasItem.CustomItem -> {
                    item.description?.let { desc ->
                        Text(
                            text = desc,
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.workoutColors.textSecondary,
                            maxLines = 1
                        )
                    }
                }
            }
        }
    }
}

/**
 * 时间轴画布（占位实现）
 */
@Composable
private fun TimelineCanvas(
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    dragState: UnifiedDragCoordinator.DragState,
    dropZones: List<UnifiedDragCoordinator.DropZone>,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "时间轴布局模式\n开发中...",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.workoutColors.textSecondary,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 自由画布（占位实现）
 */
@Composable
private fun FreeCanvas(
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    dragState: UnifiedDragCoordinator.DragState,
    dropZones: List<UnifiedDragCoordinator.DropZone>,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "自由画布模式\n开发中...",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.workoutColors.textSecondary,
            textAlign = TextAlign.Center
        )
    }
}

// === 辅助函数 ===

/**
 * 获取周摘要信息
 */
private fun getWeekSummary(week: Int, canvasData: PlanCanvasData): String {
    val weekItems = (1..7).sumOf { day ->
        val dayIndex = (week - 1) * 7 + day
        canvasData.getItemsForDate(dayIndex).size
    }
    return if (weekItems > 0) "$weekItems 个项目" else "无安排"
}

/**
 * 获取星期名称
 */
private fun getDayName(day: Int): String {
    return when (day) {
        1 -> "周一"
        2 -> "周二"
        3 -> "周三"
        4 -> "周四"
        5 -> "周五"
        6 -> "周六"
        7 -> "周日"
        else -> "第${day}天"
    }
}