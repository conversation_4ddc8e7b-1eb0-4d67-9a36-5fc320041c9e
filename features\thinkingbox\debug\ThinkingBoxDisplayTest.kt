package com.example.gymbro.features.thinkingbox.debug

import kotlinx.coroutines.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【726task修复验证】ThinkingBox显示测试
 * 
 * 测试修复后ThinkingBox是否能正确显示内容
 */
@Singleton
class ThinkingBoxDisplayTest @Inject constructor(
    private val tokenRouter: com.example.gymbro.core.network.router.TokenRouter,
    private val tokenFlowValidation: TokenFlowValidation,
) {
    
    companion object {
        private const val TAG = "ThinkingBoxDisplayTest"
    }
    
    /**
     * 执行完整的ThinkingBox显示测试
     */
    suspend fun runDisplayTest(): TestResult {
        return try {
            Timber.tag(TAG).i("🚀 开始ThinkingBox显示测试...")
            
            // 1. 验证Token流基础设施
            val flowValidation = tokenFlowValidation.validateTokenFlow()
            if (!flowValidation.success) {
                return TestResult(false, "Token流验证失败: ${flowValidation.message}")
            }
            
            // 2. 模拟真实的AI响应token流
            val simulationResult = simulateAiResponseFlow()
            if (!simulationResult.success) {
                return TestResult(false, "AI响应模拟失败: ${simulationResult.message}")
            }
            
            // 3. 验证ThinkingBox状态更新
            val stateResult = validateThinkingBoxState()
            if (!stateResult.success) {
                return TestResult(false, "ThinkingBox状态验证失败: ${stateResult.message}")
            }
            
            Timber.tag(TAG).i("✅ ThinkingBox显示测试成功！")
            TestResult(true, "所有测试通过，ThinkingBox应该能正确显示")
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ ThinkingBox显示测试异常")
            TestResult(false, "测试异常: ${e.message}")
        }
    }
    
    /**
     * 模拟真实的AI响应token流
     */
    private suspend fun simulateAiResponseFlow(): TestResult {
        return try {
            Timber.tag(TAG).d("🎭 模拟AI响应token流...")
            
            val testMessageId = "test-ai-response-${System.currentTimeMillis()}"
            
            // 模拟典型的AI思考过程token序列
            val aiTokens = listOf(
                "<think>",
                "我需要分析这个问题",
                "</think>",
                "<thinking>",
                "<phase id=\"1\">",
                "<title>问题分析</title>",
                "首先，我需要理解用户的需求...",
                "</phase>",
                "<phase id=\"2\">",
                "<title>解决方案</title>",
                "基于分析，我建议...",
                "</phase>",
                "</thinking>",
                "<final>",
                "最终答案：根据分析，建议采用以下方案...",
                "</final>"
            )
            
            // 通过TokenRouter发送tokens
            aiTokens.forEach { token ->
                tokenRouter.routeToken(testMessageId, token)
                delay(50) // 模拟流式延迟
            }
            
            // 验证ConversationScope是否接收到所有tokens
            val scope = tokenRouter.getOrCreateScope(testMessageId)
            var tokenCount = 0
            
            val job = CoroutineScope(Dispatchers.IO).launch {
                scope.tokens.take(aiTokens.size).collect { token ->
                    tokenCount++
                    Timber.tag(TAG).v("收到token: $token")
                }
            }
            
            delay(2000) // 等待2秒收集
            job.cancel()
            
            // 清理
            tokenRouter.releaseScope(testMessageId)
            
            if (tokenCount != aiTokens.size) {
                return TestResult(false, "Token数量不匹配: 期望${aiTokens.size}, 实际$tokenCount")
            }
            
            Timber.tag(TAG).d("✅ AI响应模拟成功，发送了${aiTokens.size}个tokens")
            TestResult(true, "AI响应模拟成功")
            
        } catch (e: Exception) {
            TestResult(false, "AI响应模拟异常: ${e.message}")
        }
    }
    
    /**
     * 验证ThinkingBox状态更新
     */
    private suspend fun validateThinkingBoxState(): TestResult {
        return try {
            Timber.tag(TAG).d("🔍 验证ThinkingBox状态...")
            
            // 检查TokenRouter的活跃scope数量
            val activeScopeIds = tokenRouter.getActiveScopeIds()
            Timber.tag(TAG).d("活跃的ConversationScope数量: ${activeScopeIds.size}")
            
            // 获取调试信息
            val debugInfo = tokenRouter.getDebugInfo()
            Timber.tag(TAG).d("TokenRouter调试信息:\n$debugInfo")
            
            // 验证基础设施是否正常
            if (activeScopeIds.isEmpty()) {
                Timber.tag(TAG).w("警告：没有活跃的ConversationScope，这可能是正常的（如果没有正在进行的对话）")
            }
            
            Timber.tag(TAG).d("✅ ThinkingBox状态验证通过")
            TestResult(true, "ThinkingBox状态正常")
            
        } catch (e: Exception) {
            TestResult(false, "ThinkingBox状态验证异常: ${e.message}")
        }
    }
    
    /**
     * 生成测试报告
     */
    fun generateTestReport(): String {
        return buildString {
            appendLine("=== ThinkingBox 726task修复测试报告 ===")
            appendLine()
            appendLine("修复内容：")
            appendLine("1. ✅ AdaptiveStreamClient添加TokenRouter依赖")
            appendLine("2. ✅ 实现双路径token发布：TokenRouter + TokenBus")
            appendLine("3. ✅ 建立正确的数据流：AdaptiveStreamClient → TokenRouter → ConversationScope → ThinkingBoxViewModel")
            appendLine("4. ✅ 保持Coach模块兼容性（TokenBus路径）")
            appendLine()
            appendLine("预期效果：")
            appendLine("- ThinkingBox应该能正确接收和显示AI思考过程")
            appendLine("- 网络连接状态应该正确反映")
            appendLine("- Coach模块的历史功能保持正常")
            appendLine()
            appendLine("测试建议：")
            appendLine("1. 启动应用并发送AI消息")
            appendLine("2. 观察ThinkingBox是否显示思考过程")
            appendLine("3. 检查日志中的TB-RAW-TOKEN和TB-VIEWMODEL标签")
            appendLine("4. 验证最终消息是否正确保存到历史记录")
        }
    }
    
    /**
     * 测试结果数据类
     */
    data class TestResult(
        val success: Boolean,
        val message: String
    )
}
