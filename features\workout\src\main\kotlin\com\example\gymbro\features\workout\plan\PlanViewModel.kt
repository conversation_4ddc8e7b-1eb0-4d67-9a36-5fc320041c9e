package com.example.gymbro.features.workout.plan

import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.features.workout.plan.internal.effect.PlanEffectHandler
import com.example.gymbro.features.workout.plan.internal.reducer.PlanReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject

/**
 * Plan模块ViewModel
 * 使用BaseMviViewModel轻量架构，集成完整MVI模式
 *
 * 职责：
 * - 持有StateFlow<State>和SharedFlow<Effect>
 * - 将Intent转发给EffectHandler
 * - 接收DomainEvent并通过Reducer更新State
 * - 分发Effect给UI层
 */
@HiltViewModel
class PlanViewModel @Inject constructor(
    private val effectHandler: PlanEffectHandler,
    private val planReducer: PlanReducer,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val logger: Logger,
) : BaseMviViewModel<PlanContract.Intent, PlanContract.State, PlanContract.Effect>(
    initialState = PlanContract.State(),
) {
    // 提供Reducer实例给BaseMviViewModel
    override val reducer: Reducer<PlanContract.Intent, PlanContract.State, PlanContract.Effect> =
        object : Reducer<PlanContract.Intent, PlanContract.State, PlanContract.Effect> {
            override fun reduce(
                intent: PlanContract.Intent,
                state: PlanContract.State,
            ): ReduceResult<PlanContract.State, PlanContract.Effect> {
                // 1. 先让planReducer处理状态
                val reduceResult = planReducer.reduce(intent, state)

                // 2. 处理副作用
                effectHandler.handle(intent, reduceResult.newState, handlerScope, ::dispatch)

                // 3. 处理Stats相关Effect - 调用effectHandler的handleEffect
                reduceResult.effects.forEach { effect ->
                    when (effect) {
                        is PlanContract.Effect.LoadDailyStatsEffect,
                        is PlanContract.Effect.LoadWeeklyStatsEffect,
                        is PlanContract.Effect.LoadStatsForPlanEffect,
                        is PlanContract.Effect.LoadStatsForDateRangeEffect,
                        is PlanContract.Effect.RefreshStatsEffect,
                        -> {
                            effectHandler.handleEffect(
                                effect,
                                reduceResult.newState,
                                handlerScope,
                                ::dispatch,
                            )
                        }
                        else -> {
                            // 其他Effect由原有的handle方法处理
                        }
                    }
                }

                // 4. 返回reducer结果
                return reduceResult
            }
        }

    init {
        logger.d("PlanViewModel", "初始化MVI架构")
        // 初始化时加载计划列表
        dispatch(PlanContract.Intent.LoadPlans)
    }

    /**
     * ViewModel清理时取消所有正在进行的协程任务
     */

    override fun onCleared() {
        super.onCleared()
        logger.d("PlanViewModel", "清理ViewModel")
    }

    // === 便捷API - 为UI层提供友好的方法调用 ===

    /**
     * 便捷方法：加载计划列表
     */
    fun loadPlans() {
        dispatch(PlanContract.Intent.LoadPlans)
    }

    /**
     * 便捷方法：刷新计划列表
     */
    fun refreshPlans() {
        dispatch(PlanContract.Intent.RefreshPlans)
    }

    /**
     * 便捷方法：搜索计划
     */
    fun searchPlans(query: String) {
        dispatch(PlanContract.Intent.SearchPlans(query))
    }

    /**
     * 便捷方法：筛选计划
     */
    fun filterPlans(filter: PlanContract.PlanFilter) {
        dispatch(PlanContract.Intent.FilterPlans(filter))
    }

    /**
     * 便捷方法：清除搜索
     */
    fun clearSearch() {
        dispatch(PlanContract.Intent.ClearSearch)
    }

    /**
     * 便捷方法：选择计划
     */
    fun selectPlan(planId: String) {
        dispatch(PlanContract.Intent.SelectPlan(planId))
    }

    /**
     * 便捷方法：显示计划详情
     */
    fun showPlanDetail(plan: com.example.gymbro.domain.workout.model.WorkoutPlan) {
        dispatch(PlanContract.Intent.ShowPlanDetail(plan))
    }

    /**
     * 便捷方法：删除计划
     */
    fun deletePlan(planId: String) {
        dispatch(PlanContract.Intent.DeletePlan(planId))
    }

    /**
     * 便捷方法：确认删除计划
     */
    fun confirmDeletePlan(planId: String) {
        dispatch(PlanContract.Intent.ConfirmDeletePlan(planId))
    }

    /**
     * 便捷方法：复制计划
     */
    fun duplicatePlan(planId: String) {
        dispatch(PlanContract.Intent.DuplicatePlan(planId))
    }

    /**
     * 便捷方法：显示应用计划对话框
     */
    fun showApplyPlanDialog(plan: com.example.gymbro.domain.workout.model.WorkoutPlan) {
        dispatch(PlanContract.Intent.ShowApplyPlanDialog(plan))
    }

    /**
     * 便捷方法：应用计划到日历
     */
    fun applyPlanToCalendar(planId: String, startDate: kotlinx.datetime.LocalDate) {
        dispatch(PlanContract.Intent.ApplyPlanToCalendar(planId, startDate))
    }

    /**
     * 便捷方法：清除错误
     */
    fun clearError() {
        dispatch(PlanContract.Intent.ClearError)
    }

    /**
     * 便捷方法：关闭对话框
     */
    fun dismissDialogs() {
        dispatch(PlanContract.Intent.DismissDialogs)
    }

    // === AI快速生成计划相关方法 ===

    /**
     * 便捷方法：显示AI生成计划对话框
     */
    fun showGeneratePlanDialog() {
        dispatch(PlanContract.Intent.ShowGeneratePlanDialog)
    }

    /**
     * 便捷方法：生成快速计划
     */
    fun generateQuickPlan(prompt: String) {
        dispatch(PlanContract.Intent.GenerateQuickPlan(prompt))
    }

    /**
     * 便捷方法：关闭AI生成计划对话框
     */
    fun dismissGeneratePlanDialog() {
        dispatch(PlanContract.Intent.DismissGeneratePlanDialog)
    }

    // === 新增的UI便捷方法 ===

    /**
     * 便捷方法：生成日历JSON
     */
    fun generateCalendarJson(planId: String) {
        // 使用简单的日期，避免复杂的 kotlinx.datetime 调用
        val today = kotlinx.datetime.LocalDate(2024, 1, 1) // 临时使用固定日期
        dispatch(PlanContract.Intent.GenerateCalendarJson(planId, today))
    }

    /**
     * 便捷方法：导航到AI生成器
     */
    fun navigateToAIGenerator() {
        // 通过 Intent 触发 Effect
        dispatch(PlanContract.Intent.ShowGeneratePlanDialog)
    }

    /**
     * 便捷方法：切换搜索状态
     */
    fun toggleSearch() {
        dispatch(PlanContract.Intent.ToggleSearch)
    }

    /**
     * 便捷方法：显示筛选对话框
     */
    fun showFilterDialog() {
        // 触发筛选对话框显示Effect
        dispatch(PlanContract.Intent.ShowFilterDialog)
    }

    /**
     * 便捷方法：更新搜索查询
     */
    fun updateSearchQuery(query: String) {
        dispatch(PlanContract.Intent.UpdateSearchQuery(query))
    }

    /**
     * 便捷方法：移除筛选器
     */
    fun removeFilter(filter: PlanContract.PlanFilter) {
        dispatch(PlanContract.Intent.RemoveFilter(filter))
    }

    /**
     * 便捷方法：清除所有筛选器
     */
    fun clearAllFilters() {
        dispatch(PlanContract.Intent.ClearAllFilters)
    }

    /**
     * 便捷方法：切换Tab
     */
    fun switchTab(tab: PlanContract.PlanTab) {
        dispatch(PlanContract.Intent.SwitchTab(tab))
    }

    /**
     * 便捷方法：切换收藏状态
     */
    fun toggleFavorite(planId: String) {
        dispatch(PlanContract.Intent.TogglePlanFavorite(planId))
    }

    /**
     * 便捷方法：加载计划的统计数据
     */
    fun loadStatsForPlan(planId: String) {
        dispatch(PlanContract.Intent.LoadStatsForPlan(planId))
    }

    /**
     * 便捷方法：刷新统计数据
     */
    fun refreshStats() {
        dispatch(PlanContract.Intent.LoadDailyStats)
        dispatch(PlanContract.Intent.LoadWeeklyStats)
    }

    /**
     * 便捷方法：复制周计划
     */
    fun copyWeek(fromWeek: Int, toWeek: Int) {
        dispatch(PlanContract.Intent.CopyWeek(fromWeek, toWeek))
    }

    /**
     * 便捷方法：显示模板预览
     */
    fun showTemplatePreview(template: com.example.gymbro.domain.workout.model.template.WorkoutTemplate) {
        dispatch(PlanContract.Intent.ShowTemplatePreview(template))
    }

    /**
     * 便捷方法：隐藏模板预览
     */
    fun hideTemplatePreview() {
        dispatch(PlanContract.Intent.HideTemplatePreview)
    }
}
