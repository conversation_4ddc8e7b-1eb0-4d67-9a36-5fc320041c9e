package com.example.gymbro.features.workout.plan.canvas.model

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.datetime.LocalDate
import kotlinx.serialization.Serializable

/**
 * Plan画布数据模型 - 统一的JSON数据驱动设计
 * 
 * 🎯 核心设计理念：
 * - 统一数据源：Template、TemplateDraft、Plan数据的统一抽象
 * - JSON驱动：所有数据变更通过JSON序列化/反序列化
 * - 画布式设计：将拖拽操作抽象为画布上的元素操作
 * - 事件溯源：记录所有操作历史，支持撤销/重做
 * 
 * 🏗️ 架构特点：
 * - 不可变数据结构：确保状态管理的可预测性
 * - 类型安全：使用sealed class确保拖拽项类型安全
 * - 扩展性：易于添加新的拖拽项类型
 */
@Immutable
@Serializable
data class PlanCanvasData(
    /**
     * 画布唯一标识符
     */
    val canvasId: String,
    
    /**
     * 计划名称
     */
    val planName: String,
    
    /**
     * 画布尺寸配置
     */
    val canvasConfig: CanvasConfig,
    
    /**
     * 日程安排数据 - 核心数据结构
     * Key: 日期索引 (1-28)，Value: 该日期的训练项目列表
     */
    val scheduleItems: Map<Int, List<CanvasItem>>,
    
    /**
     * 可拖拽项源数据
     */
    val draggableSources: DraggableSources,
    
    /**
     * 操作历史记录
     */
    val operationHistory: List<CanvasOperation>,
    
    /**
     * 当前操作索引（用于撤销/重做）
     */
    val currentOperationIndex: Int,
    
    /**
     * 画布元数据
     */
    val metadata: CanvasMetadata
) {
    /**
     * 获取指定日期的训练项目
     */
    fun getItemsForDate(dayIndex: Int): List<CanvasItem> {
        return scheduleItems[dayIndex] ?: emptyList()
    }
    
    /**
     * 获取画布中所有已安排的项目数量
     */
    fun getTotalScheduledItems(): Int {
        return scheduleItems.values.sumOf { it.size }
    }
    
    /**
     * 检查是否可以撤销操作
     */
    fun canUndo(): Boolean = currentOperationIndex > 0
    
    /**
     * 检查是否可以重做操作
     */
    fun canRedo(): Boolean = currentOperationIndex < operationHistory.size - 1
    
    /**
     * 添加新的操作到历史记录
     */
    fun addOperation(operation: CanvasOperation): PlanCanvasData {
        val newHistory = operationHistory.take(currentOperationIndex + 1) + operation
        return copy(
            operationHistory = newHistory,
            currentOperationIndex = newHistory.size - 1
        )
    }

    companion object {
        /**
         * 创建空白画布
         */
        fun createEmpty(planName: String): PlanCanvasData {
            return PlanCanvasData(
                canvasId = generateCanvasId(),
                planName = planName,
                canvasConfig = CanvasConfig.default(),
                scheduleItems = emptyMap(),
                draggableSources = DraggableSources.empty(),
                operationHistory = listOf(CanvasOperation.CreateCanvas(System.currentTimeMillis())),
                currentOperationIndex = 0,
                metadata = CanvasMetadata.default()
            )
        }
        
        private fun generateCanvasId(): String {
            return "canvas_${System.currentTimeMillis()}_${(1000..9999).random()}"
        }
    }
}

/**
 * 画布配置
 */
@Immutable
@Serializable
data class CanvasConfig(
    /**
     * 画布布局模式
     */
    val layoutMode: CanvasLayoutMode,
    
    /**
     * 周数显示（1-4周）
     */
    val weekCount: Int,
    
    /**
     * 每周天数（通常为7天）
     */
    val daysPerWeek: Int,
    
    /**
     * 是否启用网格对齐
     */
    val snapToGrid: Boolean,
    
    /**
     * 是否显示时间轴
     */
    val showTimeline: Boolean
) {
    companion object {
        fun default(): CanvasConfig {
            return CanvasConfig(
                layoutMode = CanvasLayoutMode.WEEKLY_GRID,
                weekCount = 4,
                daysPerWeek = 7,
                snapToGrid = true,
                showTimeline = true
            )
        }
    }
}

/**
 * 画布布局模式
 */
@Serializable
enum class CanvasLayoutMode {
    /**
     * 周网格布局（4周 x 7天）
     */
    WEEKLY_GRID,
    
    /**
     * 时间轴布局（线性时间排列）
     */
    TIMELINE,
    
    /**
     * 自由画布布局（可拖拽到任意位置）
     */
    FREE_CANVAS
}

/**
 * 画布项目 - 统一的拖拽项抽象
 */
@Immutable
@Serializable
sealed class CanvasItem {
    abstract val id: String
    abstract val name: String
    abstract val type: CanvasItemType
    abstract val position: CanvasPosition
    abstract val metadata: Map<String, String>
    
    /**
     * Template项目
     */
    @Serializable
    data class TemplateItem(
        override val id: String,
        override val name: String,
        override val position: CanvasPosition,
        override val metadata: Map<String, String>,
        val templateId: String,
        val estimatedDuration: Int?,
        val exerciseCount: Int,
        val targetMuscleGroups: List<String>
    ) : CanvasItem() {
        override val type: CanvasItemType = CanvasItemType.TEMPLATE
        
        companion object {
            fun fromTemplate(template: WorkoutTemplate, position: CanvasPosition): TemplateItem {
                return TemplateItem(
                    id = "template_${template.id}",
                    name = template.name,
                    position = position,
                    metadata = mapOf(
                        "source" to "template",
                        "originalId" to template.id
                    ),
                    templateId = template.id,
                    estimatedDuration = template.estimatedDuration,
                    exerciseCount = template.exercises.size,
                    targetMuscleGroups = template.targetMuscleGroups ?: emptyList()
                )
            }
        }
    }
    
    /**
     * TemplateDraft项目
     */
    @Serializable
    data class DraftItem(
        override val id: String,
        override val name: String,
        override val position: CanvasPosition,
        override val metadata: Map<String, String>,
        val draftId: String,
        val estimatedDuration: Int?,
        val exerciseCount: Int,
        val targetMuscleGroups: List<String>,
        val canPromote: Boolean
    ) : CanvasItem() {
        override val type: CanvasItemType = CanvasItemType.DRAFT
        
        companion object {
            fun fromDraft(draft: TemplateDraft, position: CanvasPosition): DraftItem {
                return DraftItem(
                    id = "draft_${draft.id}",
                    name = draft.name,
                    position = position,
                    metadata = mapOf(
                        "source" to "draft",
                        "originalId" to draft.id,
                        "version" to draft.version.toString()
                    ),
                    draftId = draft.id,
                    estimatedDuration = draft.estimatedDuration,
                    exerciseCount = draft.exercises.size,
                    targetMuscleGroups = draft.targetMuscleGroups,
                    canPromote = draft.canPromoteToTemplate()
                )
            }
        }
    }
    
    /**
     * 自定义训练项目
     */
    @Serializable
    data class CustomItem(
        override val id: String,
        override val name: String,
        override val position: CanvasPosition,
        override val metadata: Map<String, String>,
        val description: String?,
        val estimatedDuration: Int?
    ) : CanvasItem() {
        override val type: CanvasItemType = CanvasItemType.CUSTOM
    }
}

/**
 * 画布项目类型
 */
@Serializable
enum class CanvasItemType {
    TEMPLATE,   // 正式模板
    DRAFT,      // 草稿模板
    CUSTOM      // 自定义项目
}

/**
 * 画布位置信息
 */
@Immutable
@Serializable
data class CanvasPosition(
    /**
     * 周索引 (1-4)
     */
    val week: Int,
    
    /**
     * 日索引 (1-7)
     */
    val day: Int,
    
    /**
     * 在该日期中的排序索引
     */
    val order: Int,
    
    /**
     * X坐标（用于自由画布模式）
     */
    val x: Float = 0f,
    
    /**
     * Y坐标（用于自由画布模式）
     */
    val y: Float = 0f
) {
    /**
     * 获取日期索引（1-28）
     */
    fun getDayIndex(): Int = (week - 1) * 7 + day
    
    companion object {
        /**
         * 从日期索引创建位置
         */
        fun fromDayIndex(dayIndex: Int, order: Int = 0): CanvasPosition {
            val week = (dayIndex - 1) / 7 + 1
            val day = (dayIndex - 1) % 7 + 1
            return CanvasPosition(week = week, day = day, order = order)
        }
    }
}

/**
 * 可拖拽源数据
 */
@Immutable
@Serializable
data class DraggableSources(
    /**
     * 可用的模板列表
     */
    val templates: List<TemplateSource>,
    
    /**
     * 可用的草稿列表  
     */
    val drafts: List<DraftSource>
) {
    companion object {
        fun empty(): DraggableSources {
            return DraggableSources(
                templates = emptyList(),
                drafts = emptyList()
            )
        }
    }
}

/**
 * 模板源数据
 */
@Immutable
@Serializable
data class TemplateSource(
    val id: String,
    val name: String,
    val summary: String,
    val estimatedDuration: Int?,
    val exerciseCount: Int,
    val targetMuscleGroups: List<String>,
    val tags: List<String>
) {
    companion object {
        fun fromTemplate(template: WorkoutTemplate): TemplateSource {
            return TemplateSource(
                id = template.id,
                name = template.name.toString(),
                summary = "${template.exercises.size} 个动作",
                estimatedDuration = template.estimatedDuration,
                exerciseCount = template.exercises.size,
                targetMuscleGroups = template.targetMuscleGroups ?: emptyList(),
                tags = template.tags ?: emptyList()
            )
        }
    }
}

/**
 * 草稿源数据
 */
@Immutable
@Serializable
data class DraftSource(
    val id: String,
    val name: String,
    val summary: String,
    val estimatedDuration: Int?,
    val exerciseCount: Int,
    val targetMuscleGroups: List<String>,
    val tags: List<String>,
    val canPromote: Boolean,
    val version: Int
) {
    companion object {
        fun fromDraft(draft: TemplateDraft): DraftSource {
            return DraftSource(
                id = draft.id,
                name = draft.name,
                summary = "${draft.exercises.size} 个动作",
                estimatedDuration = draft.estimatedDuration,
                exerciseCount = draft.exercises.size,
                targetMuscleGroups = draft.targetMuscleGroups,
                tags = draft.tags,
                canPromote = draft.canPromoteToTemplate(),
                version = draft.version
            )
        }
    }
}

/**
 * 画布操作记录 - 支持撤销/重做
 */
@Serializable
sealed class CanvasOperation {
    abstract val timestamp: Long
    
    @Serializable
    data class CreateCanvas(override val timestamp: Long) : CanvasOperation()
    
    @Serializable
    data class AddItem(
        override val timestamp: Long,
        val item: CanvasItem,
        val position: CanvasPosition
    ) : CanvasOperation()
    
    @Serializable
    data class MoveItem(
        override val timestamp: Long,
        val itemId: String,
        val fromPosition: CanvasPosition,
        val toPosition: CanvasPosition
    ) : CanvasOperation()
    
    @Serializable
    data class RemoveItem(
        override val timestamp: Long,
        val item: CanvasItem
    ) : CanvasOperation()
    
    @Serializable
    data class UpdateItem(
        override val timestamp: Long,
        val itemId: String,
        val oldItem: CanvasItem,
        val newItem: CanvasItem
    ) : CanvasOperation()
}

/**
 * 画布元数据
 */
@Immutable
@Serializable
data class CanvasMetadata(
    val createdAt: Long,
    val updatedAt: Long,
    val version: Int,
    val author: String,
    val description: String?
) {
    companion object {
        fun default(): CanvasMetadata {
            val now = System.currentTimeMillis()
            return CanvasMetadata(
                createdAt = now,
                updatedAt = now,
                version = 1,
                author = "current_user",
                description = null
            )
        }
    }
}