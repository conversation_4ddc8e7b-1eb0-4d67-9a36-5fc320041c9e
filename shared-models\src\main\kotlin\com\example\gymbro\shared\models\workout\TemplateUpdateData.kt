package com.example.gymbro.shared.models.workout

import kotlinx.serialization.Serializable

/**
 * 模板更新数据类
 *
 * 用于更新训练模板的部分信息，支持可选字段更新
 * 遵循shared-models模块的纯DTO设计原则，不包含业务逻辑
 *
 * @property name 模板名称（可选更新）
 * @property description 模板描述（可选更新）
 * @property targetDuration 目标时长分钟数（可选更新）
 * @property difficulty 难度等级（可选更新）
 * @property category 分类标签（可选更新）
 * @property isActive 是否激活（可选更新）
 * @property exerciseUpdates 动作更新列表（可选更新）
 */
@Serializable
data class TemplateUpdateData(
    val name: String? = null,
    val description: String? = null,
    val targetDuration: Int? = null,
    val difficulty: String? = null,
    val category: String? = null,
    val isActive: Boolean? = null,
    val exerciseUpdates: List<ExerciseUpdateData>? = null,
) {
    companion object {
        /**
         * 创建空的更新数据
         */
        fun empty(): TemplateUpdateData = TemplateUpdateData()

        /**
         * 仅更新名称
         */
        fun nameOnly(name: String): TemplateUpdateData = TemplateUpdateData(name = name)

        /**
         * 仅更新描述
         */
        fun descriptionOnly(description: String): TemplateUpdateData = TemplateUpdateData(description = description)

        /**
         * 仅更新状态
         */
        fun statusOnly(isActive: Boolean): TemplateUpdateData = TemplateUpdateData(isActive = isActive)
    }

    /**
     * 检查是否有任何字段需要更新
     */
    fun hasUpdates(): Boolean {
        return name != null ||
            description != null ||
            targetDuration != null ||
            difficulty != null ||
            category != null ||
            isActive != null ||
            exerciseUpdates != null
    }

    /**
     * 获取更新字段的数量
     */
    fun getUpdateCount(): Int {
        var count = 0
        if (name != null) count++
        if (description != null) count++
        if (targetDuration != null) count++
        if (difficulty != null) count++
        if (category != null) count++
        if (isActive != null) count++
        if (exerciseUpdates != null) count++
        return count
    }
}