package com.example.gymbro.features.workout.template.internal.reducer

import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 模板UI处理器
 *
 * 负责处理用户界面相关逻辑：
 * - 手势操作 (滑动/重排序)
 * - 对话框管理
 * - 导航管理
 * - 错误处理
 * - Tab切换
 * - 内部状态更新
 */
@Singleton
class TemplateReducerUIHandlers @Inject constructor() {

    // === 手势操作处理函数 ===

    fun handleStartSwipe(intent: TemplateContract.Intent.StartSwipe, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                swipeStates = state.swipeStates + (intent.exerciseId to TemplateContract.SwipeState.Swiping(0f)),
            ),
        )
    }

    fun handleUpdateSwipe(intent: TemplateContract.Intent.UpdateSwipe, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                swipeStates = state.swipeStates + (intent.exerciseId to TemplateContract.SwipeState.Swiping(intent.offset)),
            ),
        )
    }

    fun handleCompleteSwipe(intent: TemplateContract.Intent.CompleteSwipe, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        // 完成滑动删除操作 - 需要调用业务处理器的移除方法
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                swipeStates = state.swipeStates - intent.exerciseId,
            ),
        )
    }

    fun handleCancelSwipe(intent: TemplateContract.Intent.CancelSwipe, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                swipeStates = state.swipeStates - intent.exerciseId,
            ),
        )
    }

    // === 错误处理函数 ===

    fun handleClearError(intent: TemplateContract.Intent.ClearError, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(error = null),
        )
    }

    fun handleClearSaveError(intent: TemplateContract.Intent.ClearSaveError, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(saveError = null),
        )
    }

    fun handleClearNetworkError(intent: TemplateContract.Intent.ClearNetworkError, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(networkError = null),
        )
    }

    fun handleRetryLastOperation(intent: TemplateContract.Intent.RetryLastOperation, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return if (state.lastFailedOperation != null) {
            TemplateReducer.ReduceResult.stateOnly(
                state.copy(
                    error = null,
                    saveError = null,
                    networkError = null,
                ),
            )
        } else {
            TemplateReducer.ReduceResult.stateOnly(state)
        }
    }

    fun handleHandleError(intent: TemplateContract.Intent.HandleError, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                error = intent.error,
                lastFailedOperation = intent.operation,
                isLoading = false,
                isSaving = false,
                isDeleting = false,
            ),
            effect = TemplateContract.Effect.ShowSnackbar(
                message = intent.error,
                actionLabel = if (intent.operation != null) "重试" else null,
            ),
        )
    }

    // === 对话框管理处理函数 ===

    fun handleShowDeleteDialog(intent: TemplateContract.Intent.ShowDeleteDialog, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(showDeleteDialog = true),
        )
    }

    fun handleHideDeleteDialog(intent: TemplateContract.Intent.HideDeleteDialog, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                showDeleteDialog = false,
                deleteTargetId = null,
            ),
        )
    }

    fun handleConfirmDelete(intent: TemplateContract.Intent.ConfirmDelete, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                showDeleteDialog = false,
                isDeleting = true,
                deleteTargetId = intent.templateId,
            ),
        )
    }

    fun handleShowSaveConfirmDialog(intent: TemplateContract.Intent.ShowSaveConfirmDialog, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(showSaveConfirmDialog = true),
            effect = TemplateContract.Effect.ShowUnsavedChangesDialog,
        )
    }

    fun handleHideSaveConfirmDialog(intent: TemplateContract.Intent.HideSaveConfirmDialog, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(showSaveConfirmDialog = false),
        )
    }

    fun handleConfirmSave(intent: TemplateContract.Intent.ConfirmSave, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return if (state.editingTemplate != null) {
            TemplateReducer.ReduceResult.stateOnly(
                state.copy(
                    showSaveConfirmDialog = false,
                    isSaving = true,
                ),
            )
        } else {
            TemplateReducer.ReduceResult.stateOnly(
                state.copy(showSaveConfirmDialog = false),
            )
        }
    }

    fun handleDiscardChanges(intent: TemplateContract.Intent.DiscardChanges, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                showSaveConfirmDialog = false,
                editingTemplate = null,
                hasUnsavedChanges = false,
            ),
            effect = TemplateContract.Effect.NavigateBack,
        )
    }

    // === Tab切换处理函数 ===

    fun handleSwitchTab(intent: TemplateContract.Intent.SwitchTab, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(currentTab = intent.tab),
        )
    }

    // === 导航处理函数 ===

    fun handleNavigateToTemplateDetail(intent: TemplateContract.Intent.NavigateToTemplateDetail, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(navigationPending = true),
            effect = TemplateContract.Effect.NavigateToTemplateDetail(intent.templateId),
        )
    }

    fun handleNavigateToEditTemplate(intent: TemplateContract.Intent.NavigateToEditTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(navigationPending = true),
            effect = TemplateContract.Effect.NavigateToEditTemplate(intent.templateId),
        )
    }

    fun handleNavigateToNewTemplate(intent: TemplateContract.Intent.NavigateToNewTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(navigationPending = true),
            effect = TemplateContract.Effect.NavigateToNewTemplate,
        )
    }

    fun handleNavigateToDraftEditor(intent: TemplateContract.Intent.NavigateToDraftEditor, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(navigationPending = true),
            effect = TemplateContract.Effect.NavigateToDraftEditor(intent.draftId),
        )
    }

    fun handleNavigateToCreateDraft(intent: TemplateContract.Intent.NavigateToCreateDraft, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(navigationPending = true),
            effect = TemplateContract.Effect.NavigateToCreateDraft,
        )
    }

    fun handleResetNavigationState(intent: TemplateContract.Intent.ResetNavigationState, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(navigationPending = false),
        )
    }

    fun handleNavigateBack(intent: TemplateContract.Intent.NavigateBack, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(navigationPending = true),
            effect = TemplateContract.Effect.NavigateBack,
        )
    }

    // === 内部状态更新处理函数 ===

    fun handleTemplatesLoaded(intent: TemplateContract.Intent.TemplatesLoaded, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        // 🔥 修复：应用用户自定义排序到加载的模板
        val sortedTemplates = applySortOrder(intent.templates, state.templateOrder)

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                templates = intent.templates,
                filteredTemplates = sortedTemplates,
                isLoading = false,
                error = null,
            ),
        )
    }

    fun handleDraftsLoaded(intent: TemplateContract.Intent.DraftsLoaded, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        // 🔥 修复：应用用户自定义排序到加载的草稿
        val sortedDrafts = applySortOrder(intent.drafts, state.draftOrder)

        return TemplateReducer.ReduceResult.stateOnly(
            state.copy(
                drafts = intent.drafts,
                filteredDrafts = sortedDrafts,
                isLoadingDrafts = false,
                error = null,
            ),
        )
    }

    fun handleTemplateSaved(intent: TemplateContract.Intent.TemplateSaved, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val updatedTemplates = state.templates.map { template ->
            if (template.id == intent.template.id) {
                intent.template
            } else {
                template
            }
        }.let { templates ->
            if (templates.none { it.id == intent.template.id }) {
                templates + intent.template
            } else {
                templates
            }
        }

        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                templates = updatedTemplates,
                filteredTemplates = updatedTemplates,
                editingTemplate = intent.template,
                isSaving = false,
                hasUnsavedChanges = false,
                lastSaveTime = System.currentTimeMillis(),
                saveError = null,
            ),
            effect = TemplateContract.Effect.ShowToast(UiText.DynamicString("模板保存成功")),
        )
    }

    fun handleTemplateDeleted(intent: TemplateContract.Intent.TemplateDeleted, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val updatedTemplates = state.templates.filter { it.id != intent.templateId }

        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                templates = updatedTemplates,
                filteredTemplates = updatedTemplates,
                isDeleting = false,
                deleteTargetId = null,
                animatingItems = state.animatingItems + intent.templateId,
            ),
            effect = TemplateContract.Effect.ShowToast(UiText.DynamicString("模板删除成功")),
        )
    }

    fun handleCacheRestored(intent: TemplateContract.Intent.CacheRestored, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        val cacheMap = intent.templates.associateBy { it.id }

        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                cachedChanges = cacheMap,
                cacheAvailable = true,
            ),
            effect = TemplateContract.Effect.ShowSnackbar(
                message = UiText.DynamicString("发现 ${intent.templates.size} 个未保存的更改"),
                actionLabel = "恢复",
            ),
        )
    }

    fun handleSaveError(intent: TemplateContract.Intent.SaveError, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isSaving = false,
                saveError = intent.error,
                // 🔥 修复：移除 SaveTemplate Intent 引用
                lastFailedOperation = null, // SaveTemplate Intent 已被移除
            ),
            effect = TemplateContract.Effect.ShowSnackbar(
                message = intent.error,
                actionLabel = "重试",
            ),
        )
    }

    fun handleLoadError(intent: TemplateContract.Intent.LoadError, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isLoading = false,
                isLoadingDrafts = false,
                error = intent.error,
            ),
            effect = TemplateContract.Effect.ShowSnackbar(
                message = intent.error,
                actionLabel = "重试",
            ),
        )
    }

    fun handleNetworkError(intent: TemplateContract.Intent.NetworkError, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                networkError = intent.error,
                isLoading = false,
                isSaving = false,
            ),
            effect = TemplateContract.Effect.ShowSnackbar(
                message = intent.error,
                actionLabel = "重试",
            ),
        )
    }

    fun handleSaveSuccess(intent: TemplateContract.Intent.SaveSuccess, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isSaving = false,
                hasUnsavedChanges = false,
                lastSaveTime = System.currentTimeMillis(),
                saveError = null,
            ),
            effect = TemplateContract.Effect.ShowToast(UiText.DynamicString("保存成功")),
        )
    }

    fun handleDeleteSuccess(intent: TemplateContract.Intent.DeleteSuccess, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateAndEffect(
            newState = state.copy(
                isDeleting = false,
                deleteTargetId = null,
            ),
            effect = TemplateContract.Effect.ShowToast(UiText.DynamicString("删除成功")),
        )
    }

    // === 兼容性Intent处理函数 ===

    fun handleLoadTemplate(intent: TemplateContract.Intent.LoadTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(state)
    }

    fun handleStartWorkout(intent: TemplateContract.Intent.StartWorkout, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(state)
    }

    fun handleSelectTemplate(intent: TemplateContract.Intent.SelectTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(state)
    }

    fun handleToggleTemplateFavorite(intent: TemplateContract.Intent.ToggleTemplateFavorite, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(state)
    }

    fun handleStartWorkoutFromTemplate(intent: TemplateContract.Intent.StartWorkoutFromTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(state)
    }

    fun handleCreateNewTemplate(intent: TemplateContract.Intent.CreateNewTemplate, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(state)
    }

    // 🔥 修复：移除已废弃的 Intent 处理方法
    // 这些方法引用了已被移除的 Intent 类型，会导致编译错误
    // 所有动作编辑功能已移至 TemplateEditScreen

    /*
    已移除的方法：
    - handleAddExerciseToTemplate: 添加动作功能移至编辑界面
    - handleRemoveExerciseFromTemplate: 移除动作功能移至编辑界面
    - handleUpdateTemplateExercise: 更新动作功能移至编辑界面
     */

    fun handleShowDeleteDraftDialog(intent: TemplateContract.Intent.ShowDeleteDraftDialog, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(state)
    }

    fun handleOnTemplatesLoaded(intent: TemplateContract.Intent.OnTemplatesLoaded, state: TemplateContract.State): TemplateReducer.ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return TemplateReducer.ReduceResult.stateOnly(state)
    }

    // === 私有辅助函数 ===

    /**
     * 应用用户自定义排序
     * 根据 sortOrder 映射对列表进行排序，没有排序信息的项目保持原有顺序并排在最后
     */
    private fun <T> applySortOrder(items: List<T>, sortOrder: Map<String, Int>): List<T>
        where T : Any {
        return items.sortedWith { a, b ->
            val aId = when (a) {
                is WorkoutTemplateDto -> a.id
                is com.example.gymbro.domain.workout.model.TemplateDraft -> a.id
                else -> return@sortedWith 0
            }
            val bId = when (b) {
                is WorkoutTemplateDto -> b.id
                is com.example.gymbro.domain.workout.model.TemplateDraft -> b.id
                else -> return@sortedWith 0
            }

            val aOrder = sortOrder[aId] ?: Int.MAX_VALUE
            val bOrder = sortOrder[bId] ?: Int.MAX_VALUE
            aOrder.compareTo(bOrder)
        }
    }
}
