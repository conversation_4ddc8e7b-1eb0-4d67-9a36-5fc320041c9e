package com.example.gymbro.features.workout.logging

import android.util.Log
import com.example.gymbro.core.logging.LoggingConfig
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Workout模块专用日志管理器
 *
 * 🎯 功能：
 * - 统一管理workout模块所有日志输出
 * - 使用WK前缀标识workout模块日志
 * - 集成core的TimberManager，遵循全局日志策略
 * - 支持模块级别的日志控制和过滤
 *
 * 🔥 使用示例：
 * ```kotlin
 * Timber.tag("WK-TEMPLATE").i("模板保存成功")
 * Timber.tag("WK-EXERCISE").d("动作添加: ${exercise.name}")
 * Timber.tag("WK-JSON").e("JSON解析失败", exception)
 * ```
 */
@Singleton
class WorkoutLogTree @Inject constructor(
    private val loggingConfig: LoggingConfig,
) : Timber.Tree() {

    companion object {
        // === 整合后的日志标签定义（减少重复）===

        /** 核心业务流程 (合并 WK-TEMPLATE + WK-CRITICAL) */
        const val TAG_CORE = "WK-CORE"

        /** 数据处理 (合并 WK-MAPPER + WK-VALIDATION + WK-DB) */
        const val TAG_DATA = "WK-DATA"

        /** 动作相关操作 */
        const val TAG_EXERCISE = "WK-EXERCISE"

        /** 调试相关 (仅关键调试信息) */
        const val TAG_DEBUG = "WK-DEBUG"

        /** 错误相关 */
        const val TAG_ERROR = "WK-ERROR"

        /** 性能相关 */
        const val TAG_PERFORMANCE = "WK-PERF"

        // === 向后兼容的旧标签（保持现有代码工作）===
        /** @deprecated 使用 TAG_CORE 替代 */
        const val TAG_TEMPLATE = TAG_CORE

        /** @deprecated 使用 TAG_DATA 替代 */
        const val TAG_JSON = TAG_DATA

        /** @deprecated 使用 TAG_DATA 替代 */
        const val TAG_DATABASE = TAG_DATA

        /** @deprecated 使用 TAG_CORE 替代 */
        const val TAG_CRITICAL = TAG_CORE

        /** @deprecated 使用 TAG_DATA 替代 */
        const val TAG_VALIDATION = TAG_DATA

        // === 统一日志方法 - 消除重复信息 ===

        /**
         * 🔥 保存链路跟踪 - 统一WK前缀保存流程日志
         */
        fun logSaveStep(step: String, templateName: String, detail: String = "") {
            val message = if (detail.isNotEmpty()) {
                "🔥 [WK-SAVE-$step] $templateName - $detail"
            } else {
                "🔥 [WK-SAVE-$step] $templateName"
            }
            Timber.tag(TAG_CORE).i(message)
        }

        /**
         * 🔥 保存错误跟踪
         */
        fun logSaveError(step: String, templateName: String, error: String) {
            Timber.tag(TAG_CORE).e("❌ [WK-SAVE-$step] $templateName - ERROR: $error")
        }

        /**
         * 统一记录模板信息，避免重复日志
         */
        fun logTemplateInfo(templateName: String, exerciseCount: Int, context: String) {
            Timber.tag(TAG_CORE).i("🔥 [$context] $templateName (${exerciseCount}动作)")
        }

        /**
         * 统一记录组数据，避免重复的详细日志
         * 🔥 优化：只在真正必要时记录详细信息
         */
        fun logExerciseDetails(
            exerciseName: String,
            setCount: Int,
            context: String,
            onlyOnError: Boolean = true,
        ) {
            // 只在错误情况下记录详细日志
            if (!onlyOnError || setCount == 0) {
                Timber.tag(TAG_EXERCISE).d("🔥 [$context] $exerciseName: ${setCount}组")
            }
        }

        /**
         * 统一JSON操作日志 - 仅记录失败情况
         */
        fun logJsonOperation(operation: String, size: Int, success: Boolean) {
            if (!success) {
                Timber.tag(TAG_DATA).e("❌ [$operation] JSON处理失败, 长度: $size")
            }
        }

        /**
         * 统一数据映射日志 - 仅记录大量数据或异常情况
         */
        fun logDataMapping(from: String, to: String, itemCount: Int) {
            // 只在数据量大或为0时记录
            if (itemCount > 10 || itemCount == 0) {
                Timber.tag(TAG_DATA).d("🔄 [$from→$to] 映射完成: $itemCount 项")
            }
        }

        // === 兼容旧标签（向前兼容）===
        private val LEGACY_TAG_MAPPING = mapOf(
            // 核心业务流程标签映射到 WK-CORE
            "CRITICAL-SAVE" to TAG_CORE,
            "CRITICAL-LOAD" to TAG_CORE,
            "CRITICAL-PUBLISH" to TAG_CORE,
            "CRITICAL-EFFECT" to TAG_CORE,
            "WK-TEMPLATE" to TAG_CORE,
            "WK-CRITICAL" to TAG_CORE,

            // 数据处理标签映射到 WK-DATA
            "WK-MAPPER" to TAG_DATA,
            "WK-VALIDATION" to TAG_DATA,
            "WK-DB" to TAG_DATA,
            "CRITICAL-DB" to TAG_DATA,
            "WK-JSON" to TAG_DATA,
            "P0-MAPPER-START" to TAG_DATA,
            "DATA-VALIDATION" to TAG_DATA,

            // 动作相关保持不变
            "WK-EXERCISE" to TAG_EXERCISE,
            "EXERCISE-ADD" to TAG_EXERCISE,
            "ADD-SOURCE" to TAG_EXERCISE,
            "ADD-RESULT" to TAG_EXERCISE,

            // 其他标签
            "SAVE-HANDLER" to TAG_CORE,
            "CRITICAL-TRANSACTION" to TAG_DATA,
            "JSON-PARSE" to TAG_DATA,
        )

        // === WK前缀相关的标签识别 ===
        private val WK_RELATED_TAGS = setOf(
            // 现有关键标签
            "CRITICAL-SAVE", "CRITICAL-LOAD", "CRITICAL-DB", "CRITICAL-TRANSACTION",
            "SAVE-HANDLER", "EXERCISE-ADD", "JSON-PARSE", "TEMPLATE-WEIGHT-DEBUG",

            // P0/Phase相关标签
            "P0-MAPPER-START", "P0-EXERCISE", "P0-SET-DATA", "P0-IMAGE-DATA",
            "P0-ID-PRESERVE", "P0-ID-FINAL", "P0-PROTECTION",

            // 数据验证相关
            "DATA-PRESERVATION", "DATA-VALIDATION", "DATA-INTEGRITY",

            // 模板编辑相关
            "TEMPLATE-NAME", /* 🔥 已移除：CRITICAL-AUTOSAVE - 自动保存功能已被移除 */ "CRITICAL-EFFECT",
            "CRITICAL-PUBLISH", "CRITICAL-SAVE-START", "CRITICAL-SAVE-SUCCESS",
            "CRITICAL-SAVE-ERROR",

            // 新增调试标签
            "ADD-SOURCE", "ADD-RESULT",

            // 🔥 新增：WK保存链路跟踪标签
            "WK-SAVE-START", "WK-SAVE-PROCESS", "WK-SAVE-VALIDATE", "WK-SAVE-CANCEL",
            "WK-SAVE-DETERMINE", "WK-SAVE-CREATE-MODE", "WK-SAVE-UPDATE-MODE",
            "WK-SAVE-NAME-FINAL", "WK-SAVE-BUILD", "WK-SAVE-EXECUTE",
            "WK-SAVE-TRANSACTION", "WK-SAVE-COMPLETE", "WK-SAVE-NOTIFY",
            "WK-SAVE-SUCCESS", "WK-SAVE-FAILED", "WK-SAVE-TX-START",
            "WK-SAVE-TX-VALIDATE", "WK-SAVE-TX-VALIDATE-OK", "WK-SAVE-TX-VALIDATE-FAILED",
            "WK-SAVE-TX-PREPARE", "WK-SAVE-TX-JSON-CHECK", "WK-SAVE-TX-FC-CHECK",
            "WK-SAVE-TX-FC-OK", "WK-SAVE-TX-ATOMIC", "WK-SAVE-TX-SUCCESS", "WK-SAVE-TX-FAILED",
        )
    }

    override fun isLoggable(tag: String?, priority: Int): Boolean {
        // 检查是否为Workout模块相关的日志
        if (!isWorkoutRelatedTag(tag)) {
            return false
        }

        // 获取模块配置
        val moduleConfig = loggingConfig.getModuleConfig(LoggingConfig.MODULE_WORKOUT)

        // 模块未启用时不记录
        if (moduleConfig?.enabled != true) {
            return false
        }

        // 检查优先级
        if (priority < moduleConfig.minLevel) {
            return false
        }

        // 检查标签过滤
        if (moduleConfig.tags.isNotEmpty()) {
            val finalTag = mapLegacyTag(tag)
            if (!moduleConfig.tags.any { allowedTag ->
                    finalTag.contains(allowedTag, ignoreCase = true) ||
                        tag?.contains(allowedTag, ignoreCase = true) == true
                }
            ) {
                return false
            }
        }

        return true
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        if (!isLoggable(tag, priority)) return

        // 转换标签
        val finalTag = formatWorkoutTag(tag)

        // 格式化消息
        val formattedMessage = formatWorkoutMessage(tag, message)

        // 输出日志
        when (priority) {
            Log.VERBOSE -> Log.v(finalTag, formattedMessage, t)
            Log.DEBUG -> Log.d(finalTag, formattedMessage, t)
            Log.INFO -> Log.i(finalTag, formattedMessage, t)
            Log.WARN -> Log.w(finalTag, formattedMessage, t)
            Log.ERROR -> Log.e(finalTag, formattedMessage, t)
            Log.ASSERT -> Log.wtf(finalTag, formattedMessage, t)
        }
    }

    /**
     * 检查是否为Workout模块相关的标签
     */
    private fun isWorkoutRelatedTag(tag: String?): Boolean {
        if (tag == null) return false

        return tag.startsWith("WK-") ||
            LEGACY_TAG_MAPPING.containsKey(tag) ||
            WK_RELATED_TAGS.contains(tag) ||
            tag.startsWith("WORKOUT-") ||
            tag.contains("TEMPLATE") ||
            tag.contains("EXERCISE") ||
            tag.contains("CRITICAL-") ||
            // 🔥 新增：WK-SAVE 保存链路标签识别
            tag.contains("WK-SAVE-") ||
            tag.startsWith("WK-SAVE-")
    }

    /**
     * 映射旧标签到新标签
     */
    private fun mapLegacyTag(tag: String?): String {
        if (tag == null) return TAG_DEBUG

        return LEGACY_TAG_MAPPING[tag] ?: tag
    }

    /**
     * 格式化Workout模块标签
     */
    private fun formatWorkoutTag(tag: String?): String {
        if (tag == null) return TAG_DEBUG

        // 如果已经是WK前缀，直接返回
        if (tag.startsWith("WK-")) {
            return tag
        }

        // 映射旧标签
        val mappedTag = LEGACY_TAG_MAPPING[tag]
        if (mappedTag != null) {
            return mappedTag
        }

        // 对于其他相关标签，添加WK前缀
        return "WK-$tag"
    }

    /**
     * 格式化Workout模块消息
     */
    private fun formatWorkoutMessage(originalTag: String?, message: String): String {
        // 如果消息已经包含表情符号前缀，直接返回
        if (message.startsWith("🔥") || message.startsWith("✅") ||
            message.startsWith("❌") || message.startsWith("⚠️") ||
            message.startsWith("🚨") || message.startsWith("🎯")
        ) {
            return message
        }

        // 根据标签类型添加合适的前缀
        val prefix = when {
            originalTag?.contains("ERROR") == true -> "❌"
            originalTag?.contains("SUCCESS") == true -> "✅"
            originalTag?.contains("CRITICAL") == true -> "🔥"
            originalTag?.contains("WARNING") == true -> "⚠️"
            originalTag?.contains("VALIDATION") == true -> "🚨"
            originalTag?.contains("PERFORMANCE") == true -> "⚡"
            else -> "🔧"
        }

        return "$prefix [WORKOUT] $message"
    }

    /**
     * 🔥 快速日志方法 - 模板相关
     */
    object Template {
        fun info(message: String) = Timber.tag(TAG_TEMPLATE).i(message)
        fun debug(message: String) = Timber.tag(TAG_TEMPLATE).d(message)
        fun error(
            message: String,
            throwable: Throwable? = null,
        ) = Timber.tag(TAG_TEMPLATE).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_TEMPLATE).w(message)
    }

    /**
     * 🔥 快速日志方法 - 动作相关
     */
    object Exercise {
        fun info(message: String) = Timber.tag(TAG_EXERCISE).i(message)
        fun debug(message: String) = Timber.tag(TAG_EXERCISE).d(message)
        fun error(
            message: String,
            throwable: Throwable? = null,
        ) = Timber.tag(TAG_EXERCISE).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_EXERCISE).w(message)
    }

    /**
     * 🔥 快速日志方法 - JSON处理相关
     */
    object Json {
        fun info(message: String) = Timber.tag(TAG_JSON).i(message)
        fun debug(message: String) = Timber.tag(TAG_JSON).d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag(TAG_JSON).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_JSON).w(message)
    }

    /**
     * 🔥 快速日志方法 - 数据库相关
     */
    object Database {
        fun info(message: String) = Timber.tag(TAG_DATABASE).i(message)
        fun debug(message: String) = Timber.tag(TAG_DATABASE).d(message)
        fun error(
            message: String,
            throwable: Throwable? = null,
        ) = Timber.tag(TAG_DATABASE).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_DATABASE).w(message)
    }

    /**
     * 🔥 快速日志方法 - 关键数据流
     */
    object Critical {
        fun info(message: String) = Timber.tag(TAG_CRITICAL).i(message)
        fun debug(message: String) = Timber.tag(TAG_CRITICAL).d(message)
        fun error(
            message: String,
            throwable: Throwable? = null,
        ) = Timber.tag(TAG_CRITICAL).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_CRITICAL).w(message)
    }

    /**
     * 🔥 数据验证日志
     */
    object Validation {
        fun success(message: String) = Timber.tag(TAG_VALIDATION).i("✅ $message")
        fun failure(message: String) = Timber.tag(TAG_VALIDATION).e("❌ $message")
        fun warning(message: String) = Timber.tag(TAG_VALIDATION).w("⚠️ $message")
    }
}